from typing import Annotated, Any, cast

from fastapi import APIRouter, Depends, Request, BackgroundTasks
from fastcrud.paginated import PaginatedListResponse, compute_offset, paginated_response
from sqlalchemy.ext.asyncio import AsyncSession

from ...api.dependencies import get_current_user
from ...core.db.database import async_get_db
from ...core.exceptions.http_exceptions import NotFoundException, BadRequestException
from ...crud.crud_products import crud_products
from ...crud.crud_price_history import crud_price_history
from ...schemas.product import ProductRead, ProductScrapeRequest, ProductSearchRequest
from ...schemas.price_history import PriceHistoryRead

router = APIRouter(tags=["products"])


@router.get("/products/", response_model=PaginatedListResponse[ProductRead])
async def read_products(
    request: Request,
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 10,
    category: str | None = None,
    brand: str | None = None,
    min_price: float | None = None,
    max_price: float | None = None,
    search: str | None = None,
) -> dict:
    """
    Get products with optional filters:
    - category: Filter by product category
    - brand: Filter by product brand
    - min_price: Minimum price filter
    - max_price: Maximum price filter
    - search: Search in product title
    """
    filters = {"is_deleted": False}
    
    if category:
        filters["category"] = category
    if brand:
        filters["brand"] = brand
    if min_price is not None:
        filters["current_price__gte"] = min_price
    if max_price is not None:
        filters["current_price__lte"] = max_price
    if search:
        filters["title__icontains"] = search

    products_data = await crud_products.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        schema_to_select=ProductRead,
        **filters,
    )

    response: dict[str, Any] = paginated_response(crud_data=products_data, page=page, items_per_page=items_per_page)
    return response


@router.get("/products/{product_id}", response_model=ProductRead)
async def read_product(
    request: Request,
    product_id: int,
    db: Annotated[AsyncSession, Depends(async_get_db)]
) -> ProductRead:
    """Get a specific product by ID."""
    db_product = await crud_products.get(db=db, id=product_id, is_deleted=False, schema_to_select=ProductRead)
    if db_product is None:
        raise NotFoundException("Product not found")

    return cast(ProductRead, db_product)


@router.get("/products/asin/{amazon_asin}", response_model=ProductRead)
async def read_product_by_asin(
    request: Request,
    amazon_asin: str,
    db: Annotated[AsyncSession, Depends(async_get_db)]
) -> ProductRead:
    """Get a specific product by Amazon ASIN."""
    db_product = await crud_products.get(db=db, amazon_asin=amazon_asin, is_deleted=False, schema_to_select=ProductRead)
    if db_product is None:
        raise NotFoundException("Product not found")

    return cast(ProductRead, db_product)


@router.get("/products/{product_id}/price-history", response_model=PaginatedListResponse[PriceHistoryRead])
async def read_product_price_history(
    request: Request,
    product_id: int,
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 50,
) -> dict:
    """Get price history for a specific product."""
    # First check if product exists
    db_product = await crud_products.get(db=db, id=product_id, is_deleted=False)
    if db_product is None:
        raise NotFoundException("Product not found")

    price_history_data = await crud_price_history.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        product_id=product_id,
        schema_to_select=PriceHistoryRead,
    )

    response: dict[str, Any] = paginated_response(crud_data=price_history_data, page=page, items_per_page=items_per_page)
    return response


@router.post("/products/scrape")
async def scrape_product(
    request: Request,
    scrape_request: ProductScrapeRequest,
    background_tasks: BackgroundTasks,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """
    Scrape a product from Amazon URL.
    This endpoint will be implemented when the scraping system is ready.
    """
    # TODO: Implement actual scraping logic
    # For now, return a placeholder response
    
    if not scrape_request.url.startswith("https://www.amazon.in/"):
        raise BadRequestException("Only Amazon India URLs are supported")
    
    # TODO: Add background task for scraping
    # background_tasks.add_task(scrape_amazon_product, scrape_request.url, current_user["id"])
    
    return {
        "message": "Product scraping initiated",
        "url": scrape_request.url,
        "status": "pending"
    }


@router.post("/products/search")
async def search_products(
    request: Request,
    search_request: ProductSearchRequest,
    background_tasks: BackgroundTasks,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """
    Search and scrape products from Amazon search results.
    This endpoint will be implemented when the scraping system is ready.
    """
    # TODO: Implement actual search scraping logic
    # For now, return a placeholder response
    
    # TODO: Add background task for search scraping
    # background_tasks.add_task(scrape_amazon_search, search_request.query, search_request.max_results, current_user["id"])
    
    return {
        "message": "Product search scraping initiated",
        "query": search_request.query,
        "max_results": search_request.max_results,
        "status": "pending"
    }

from fastapi import APIRouter

from .deals import router as deals_router
from .login import router as login_router
from .logout import router as logout_router
from .posts import router as posts_router
from .product_alerts import router as product_alerts_router
from .products import router as products_router
from .rate_limits import router as rate_limits_router
from .tasks import router as tasks_router
from .tiers import router as tiers_router
from .user_favorites import router as user_favorites_router
from .users import router as users_router

router = APIRouter(prefix="/v1")
router.include_router(login_router)
router.include_router(logout_router)
router.include_router(users_router)
router.include_router(user_favorites_router)
router.include_router(product_alerts_router)
router.include_router(products_router)
router.include_router(deals_router)
router.include_router(posts_router)
router.include_router(tasks_router)
router.include_router(tiers_router)
router.include_router(rate_limits_router)

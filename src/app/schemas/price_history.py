from datetime import datetime
from decimal import Decimal
from typing import Annotated
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from ..models.product import AvailabilityStatus


class PriceHistoryBase(BaseModel):
    product_id: int
    original_price: Annotated[Decimal | None, Field(default=None, ge=0, examples=[79900.00])]
    discount_price: Annotated[Decimal | None, Field(default=None, ge=0, examples=[69900.00])]
    discount_percentage: Annotated[int | None, Field(default=None, ge=0, le=100, examples=[12])]
    availability_status: AvailabilityStatus = AvailabilityStatus.IN_STOCK


class PriceHistory(PriceHistoryBase):
    model_config = ConfigDict(from_attributes=True)


class PriceHistoryRead(PriceHistoryBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    uuid: UUID
    scraped_at: datetime
    created_at: datetime


class PriceHistoryCreate(PriceHistoryBase):
    pass


class PriceHistoryCreateInternal(PriceHistoryCreate):
    pass


class PriceHistoryUpdate(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    original_price: Annotated[Decimal | None, Field(default=None, ge=0)]
    discount_price: Annotated[Decimal | None, Field(default=None, ge=0)]
    discount_percentage: Annotated[int | None, Field(default=None, ge=0, le=100)]
    availability_status: AvailabilityStatus | None = None


class PriceHistoryUpdateInternal(PriceHistoryUpdate):
    pass


class PriceHistoryDelete(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    is_deleted: bool
    deleted_at: datetime

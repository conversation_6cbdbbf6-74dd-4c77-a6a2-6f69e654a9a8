from typing import Annotated, Any, cast

from fastapi import APIRouter, Depends, Request
from fastcrud.paginated import PaginatedListResponse, compute_offset, paginated_response
from sqlalchemy.ext.asyncio import AsyncSession

from ...api.dependencies import get_current_user
from ...core.db.database import async_get_db
from ...core.exceptions.http_exceptions import NotFoundException, ForbiddenException
from ...crud.crud_product_alerts import crud_product_alerts
from ...crud.crud_products import crud_products
from ...schemas.product_alert import (
    ProductAlertRead, 
    ProductAlertCreate, 
    ProductAlertCreateInternal, 
    ProductAlertUpdate,
    ProductAlertUpdateInternal
)

router = APIRouter(tags=["alerts"])


@router.get("/alerts/", response_model=PaginatedListResponse[ProductAlertRead])
async def read_user_alerts(
    request: Request,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 10,
    is_active: bool | None = None,
) -> dict:
    """Get current user's price alerts."""
    filters = {
        "user_id": current_user["id"],
        "is_deleted": False
    }
    
    if is_active is not None:
        filters["is_active"] = is_active

    alerts_data = await crud_product_alerts.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        schema_to_select=ProductAlertRead,
        **filters,
    )

    response: dict[str, Any] = paginated_response(crud_data=alerts_data, page=page, items_per_page=items_per_page)
    return response


@router.post("/alerts/", response_model=ProductAlertRead, status_code=201)
async def create_price_alert(
    request: Request,
    alert: ProductAlertCreate,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> ProductAlertRead:
    """Create a new price alert for the current user."""
    # Check if product exists
    db_product = await crud_products.get(db=db, id=alert.product_id, is_deleted=False)
    if db_product is None:
        raise NotFoundException("Product not found")

    # Create alert
    alert_create = ProductAlertCreateInternal(
        user_id=current_user["id"],
        **alert.model_dump()
    )
    
    created_alert = await crud_product_alerts.create(db=db, object=alert_create)
    
    alert_read = await crud_product_alerts.get(
        db=db, 
        id=created_alert.id, 
        schema_to_select=ProductAlertRead
    )
    if alert_read is None:
        raise NotFoundException("Created alert not found")

    return cast(ProductAlertRead, alert_read)


@router.get("/alerts/{alert_id}", response_model=ProductAlertRead)
async def read_price_alert(
    request: Request,
    alert_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)]
) -> ProductAlertRead:
    """Get a specific price alert by ID."""
    db_alert = await crud_product_alerts.get(
        db=db, 
        id=alert_id, 
        is_deleted=False, 
        schema_to_select=ProductAlertRead
    )
    if db_alert is None:
        raise NotFoundException("Alert not found")

    db_alert = cast(ProductAlertRead, db_alert)
    
    # Check if alert belongs to current user
    if db_alert.user_id != current_user["id"]:
        raise ForbiddenException("Access denied")

    return db_alert


@router.put("/alerts/{alert_id}", response_model=dict[str, str])
async def update_price_alert(
    request: Request,
    alert_id: int,
    values: ProductAlertUpdate,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Update a price alert."""
    # Check if alert exists and belongs to user
    db_alert = await crud_product_alerts.get(
        db=db, 
        id=alert_id, 
        is_deleted=False, 
        schema_to_select=ProductAlertRead
    )
    if db_alert is None:
        raise NotFoundException("Alert not found")

    db_alert = cast(ProductAlertRead, db_alert)
    
    if db_alert.user_id != current_user["id"]:
        raise ForbiddenException("Access denied")

    # Update alert
    await crud_product_alerts.update(db=db, object=values, id=alert_id)
    return {"message": "Alert updated successfully"}


@router.delete("/alerts/{alert_id}")
async def delete_price_alert(
    request: Request,
    alert_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Delete a price alert."""
    # Check if alert exists and belongs to user
    db_alert = await crud_product_alerts.get(
        db=db, 
        id=alert_id, 
        is_deleted=False, 
        schema_to_select=ProductAlertRead
    )
    if db_alert is None:
        raise NotFoundException("Alert not found")

    db_alert = cast(ProductAlertRead, db_alert)
    
    if db_alert.user_id != current_user["id"]:
        raise ForbiddenException("Access denied")

    # Soft delete the alert
    await crud_product_alerts.delete(db=db, id=alert_id)
    
    return {"message": "Alert deleted successfully"}


@router.patch("/alerts/{alert_id}/toggle")
async def toggle_alert_status(
    request: Request,
    alert_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Toggle alert active status (enable/disable)."""
    # Check if alert exists and belongs to user
    db_alert = await crud_product_alerts.get(
        db=db, 
        id=alert_id, 
        is_deleted=False, 
        schema_to_select=ProductAlertRead
    )
    if db_alert is None:
        raise NotFoundException("Alert not found")

    db_alert = cast(ProductAlertRead, db_alert)
    
    if db_alert.user_id != current_user["id"]:
        raise ForbiddenException("Access denied")

    # Toggle status
    new_status = not db_alert.is_active
    update_data = ProductAlertUpdate(is_active=new_status)
    
    await crud_product_alerts.update(db=db, object=update_data, id=alert_id)
    
    status_text = "enabled" if new_status else "disabled"
    return {"message": f"Alert {status_text} successfully"}

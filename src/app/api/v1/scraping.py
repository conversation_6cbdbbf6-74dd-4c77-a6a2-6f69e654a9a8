from typing import Annotated, Any, Dict, List

from fastapi import APIRouter, Depends, Request, BackgroundTasks, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from ...api.dependencies import get_current_user
from ...core.db.database import async_get_db
from ...core.exceptions.http_exceptions import BadRequestException, NotFoundException
from ...crud.crud_products import crud_products
from ...schemas.product import ProductScrapeRequest, ProductSearchRequest, ProductRead
from ...scraping.strategies.beautifulsoup_scraper import BeautifulSoupScraper
from ...scraping.strategies.selenium_scraper import SeleniumScraper
from ...scraping.strategies.playwright_scraper import Playwright<PERSON><PERSON>raper
from ...scraping.exceptions import (
    ScrapingException,
    InvalidURLException,
    ProductNotFoundException,
    CaptchaDetectedException,
    RateLimitedException,
    NetworkTimeoutException
)

router = APIRouter(tags=["scraping"])

# In-memory storage for scraping job status (in production, use Redis or database)
scraping_jobs: Dict[str, Dict[str, Any]] = {}


async def scrape_product_task(
    job_id: str,
    url: str,
    user_id: int,
    strategy: str = "beautifulsoup"
) -> None:
    """
    Background task to scrape a product.
    
    Args:
        job_id: Unique job identifier
        url: Product URL to scrape
        user_id: User ID who initiated the scraping
        strategy: Scraping strategy to use
    """
    scraping_jobs[job_id]["status"] = "running"
    scraping_jobs[job_id]["message"] = "Scraping in progress..."
    
    try:
        # Choose scraping strategy
        if strategy == "selenium":
            scraper = SeleniumScraper()
        elif strategy == "playwright":
            scraper = PlaywrightScraper()
        else:
            scraper = BeautifulSoupScraper()
        
        # Scrape product
        async with scraper:
            product_data = await scraper.scrape_product(url)
        
        # Save to database (simplified - in production, use proper database session)
        # For now, just store in job result
        scraping_jobs[job_id]["status"] = "completed"
        scraping_jobs[job_id]["message"] = "Scraping completed successfully"
        scraping_jobs[job_id]["result"] = {
            "amazon_asin": product_data.amazon_asin,
            "title": product_data.title,
            "description": product_data.description,
            "brand": product_data.brand,
            "category": product_data.category,
            "images": product_data.images,
            "current_price": float(product_data.current_price) if product_data.current_price else None,
            "original_price": float(product_data.original_price) if product_data.original_price else None,
            "discount_percentage": product_data.discount_percentage,
            "availability_status": product_data.availability_status,
            "average_rating": float(product_data.average_rating) if product_data.average_rating else None,
            "total_reviews": product_data.total_reviews,
            "scraped_at": product_data.scraped_at.isoformat() if product_data.scraped_at else None
        }
        
    except CaptchaDetectedException:
        scraping_jobs[job_id]["status"] = "failed"
        scraping_jobs[job_id]["message"] = "CAPTCHA detected. Please try again later."
        scraping_jobs[job_id]["error_type"] = "captcha"
        
    except RateLimitedException:
        scraping_jobs[job_id]["status"] = "failed"
        scraping_jobs[job_id]["message"] = "Rate limited. Please try again later."
        scraping_jobs[job_id]["error_type"] = "rate_limit"
        
    except ProductNotFoundException:
        scraping_jobs[job_id]["status"] = "failed"
        scraping_jobs[job_id]["message"] = "Product not found or page structure changed."
        scraping_jobs[job_id]["error_type"] = "product_not_found"
        
    except NetworkTimeoutException:
        scraping_jobs[job_id]["status"] = "failed"
        scraping_jobs[job_id]["message"] = "Network timeout. Please try again."
        scraping_jobs[job_id]["error_type"] = "timeout"
        
    except Exception as e:
        scraping_jobs[job_id]["status"] = "failed"
        scraping_jobs[job_id]["message"] = f"Scraping failed: {str(e)}"
        scraping_jobs[job_id]["error_type"] = "unknown"


async def scrape_search_task(
    job_id: str,
    query: str,
    max_results: int,
    user_id: int,
    strategy: str = "beautifulsoup"
) -> None:
    """
    Background task to scrape search results.
    
    Args:
        job_id: Unique job identifier
        query: Search query
        max_results: Maximum number of results
        user_id: User ID who initiated the scraping
        strategy: Scraping strategy to use
    """
    scraping_jobs[job_id]["status"] = "running"
    scraping_jobs[job_id]["message"] = f"Searching for '{query}'..."
    
    try:
        # Choose scraping strategy
        if strategy == "selenium":
            scraper = SeleniumScraper()
        elif strategy == "playwright":
            scraper = PlaywrightScraper()
        else:
            scraper = BeautifulSoupScraper()
        
        # Scrape search results
        async with scraper:
            products_data = await scraper.scrape_search_results(query, max_results)
        
        # Convert to serializable format
        results = []
        for product_data in products_data:
            results.append({
                "amazon_asin": product_data.amazon_asin,
                "title": product_data.title,
                "description": product_data.description,
                "brand": product_data.brand,
                "category": product_data.category,
                "images": product_data.images,
                "current_price": float(product_data.current_price) if product_data.current_price else None,
                "original_price": float(product_data.original_price) if product_data.original_price else None,
                "discount_percentage": product_data.discount_percentage,
                "availability_status": product_data.availability_status,
                "average_rating": float(product_data.average_rating) if product_data.average_rating else None,
                "total_reviews": product_data.total_reviews,
                "scraped_at": product_data.scraped_at.isoformat() if product_data.scraped_at else None
            })
        
        scraping_jobs[job_id]["status"] = "completed"
        scraping_jobs[job_id]["message"] = f"Found {len(results)} products"
        scraping_jobs[job_id]["result"] = {
            "query": query,
            "total_results": len(results),
            "products": results
        }
        
    except Exception as e:
        scraping_jobs[job_id]["status"] = "failed"
        scraping_jobs[job_id]["message"] = f"Search scraping failed: {str(e)}"
        scraping_jobs[job_id]["error_type"] = "unknown"


@router.post("/scrape/product")
async def scrape_product(
    request: Request,
    scrape_request: ProductScrapeRequest,
    background_tasks: BackgroundTasks,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
    strategy: str = "beautifulsoup"
) -> Dict[str, str]:
    """
    Scrape a product from Amazon URL.
    
    Args:
        scrape_request: Product scraping request
        strategy: Scraping strategy (beautifulsoup, selenium, playwright)
    """
    if not scrape_request.url.startswith("https://www.amazon.in/"):
        raise BadRequestException("Only Amazon India URLs are supported")
    
    # Validate strategy
    valid_strategies = ["beautifulsoup", "selenium", "playwright"]
    if strategy not in valid_strategies:
        raise BadRequestException(f"Invalid strategy. Must be one of: {valid_strategies}")
    
    # Check if product already exists (if not force refresh)
    if not scrape_request.force_refresh:
        # Extract ASIN and check if product exists
        from ...scraping.base_scraper import BaseScraper
        base_scraper = BeautifulSoupScraper()
        asin = base_scraper.extract_asin(scrape_request.url)
        
        if asin:
            existing_product = await crud_products.get(
                db=db, 
                amazon_asin=asin, 
                is_deleted=False,
                schema_to_select=ProductRead
            )
            if existing_product:
                return {
                    "message": "Product already exists in database",
                    "asin": asin,
                    "status": "exists"
                }
    
    # Generate job ID
    import uuid
    job_id = str(uuid.uuid4())
    
    # Initialize job status
    scraping_jobs[job_id] = {
        "status": "pending",
        "message": "Scraping job queued",
        "url": scrape_request.url,
        "strategy": strategy,
        "user_id": current_user["id"],
        "created_at": request.state.request_time if hasattr(request.state, 'request_time') else None
    }
    
    # Add background task
    background_tasks.add_task(
        scrape_product_task,
        job_id,
        scrape_request.url,
        current_user["id"],
        strategy
    )
    
    return {
        "message": "Product scraping initiated",
        "job_id": job_id,
        "url": scrape_request.url,
        "strategy": strategy,
        "status": "pending"
    }


@router.post("/scrape/search")
async def scrape_search(
    request: Request,
    search_request: ProductSearchRequest,
    background_tasks: BackgroundTasks,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
    strategy: str = "beautifulsoup"
) -> Dict[str, str]:
    """
    Search and scrape products from Amazon search results.
    
    Args:
        search_request: Product search request
        strategy: Scraping strategy (beautifulsoup, selenium, playwright)
    """
    # Validate strategy
    valid_strategies = ["beautifulsoup", "selenium", "playwright"]
    if strategy not in valid_strategies:
        raise BadRequestException(f"Invalid strategy. Must be one of: {valid_strategies}")
    
    # Generate job ID
    import uuid
    job_id = str(uuid.uuid4())
    
    # Initialize job status
    scraping_jobs[job_id] = {
        "status": "pending",
        "message": "Search scraping job queued",
        "query": search_request.query,
        "max_results": search_request.max_results,
        "strategy": strategy,
        "user_id": current_user["id"],
        "created_at": request.state.request_time if hasattr(request.state, 'request_time') else None
    }
    
    # Add background task
    background_tasks.add_task(
        scrape_search_task,
        job_id,
        search_request.query,
        search_request.max_results,
        current_user["id"],
        strategy
    )
    
    return {
        "message": "Search scraping initiated",
        "job_id": job_id,
        "query": search_request.query,
        "max_results": search_request.max_results,
        "strategy": strategy,
        "status": "pending"
    }


@router.get("/scrape/status/{job_id}")
async def get_scraping_status(
    request: Request,
    job_id: str,
    current_user: Annotated[dict, Depends(get_current_user)]
) -> Dict[str, Any]:
    """
    Check scraping job status.
    
    Args:
        job_id: Scraping job ID
    """
    if job_id not in scraping_jobs:
        raise NotFoundException("Scraping job not found")
    
    job = scraping_jobs[job_id]
    
    # Check if user owns this job
    if job["user_id"] != current_user["id"]:
        raise NotFoundException("Scraping job not found")
    
    return job


@router.delete("/scrape/status/{job_id}")
async def delete_scraping_job(
    request: Request,
    job_id: str,
    current_user: Annotated[dict, Depends(get_current_user)]
) -> Dict[str, str]:
    """
    Delete scraping job status.
    
    Args:
        job_id: Scraping job ID
    """
    if job_id not in scraping_jobs:
        raise NotFoundException("Scraping job not found")
    
    job = scraping_jobs[job_id]
    
    # Check if user owns this job
    if job["user_id"] != current_user["id"]:
        raise NotFoundException("Scraping job not found")
    
    # Delete job
    del scraping_jobs[job_id]
    
    return {"message": "Scraping job deleted successfully"}

from typing import Annotated, Any, cast

from fastapi import APIRouter, Depends, Request
from fastcrud.paginated import PaginatedListResponse, compute_offset, paginated_response
from sqlalchemy.ext.asyncio import AsyncSession

from ...api.dependencies import get_current_user, get_current_superuser
from ...core.db.database import async_get_db
from ...core.exceptions.http_exceptions import NotFoundException, ForbiddenException, BadRequestException
from ...crud.crud_deals import crud_deals
from ...crud.crud_products import crud_products
from ...schemas.deal import (
    DealRead, 
    DealCreate, 
    DealCreateInternal, 
    DealUpdate,
    DealUpdateInternal,
    DealVote
)

router = APIRouter(tags=["deals"])


@router.get("/deals/", response_model=PaginatedListResponse[DealRead])
async def read_deals(
    request: Request,
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 10,
    is_verified: bool | None = None,
    sort_by: str = "created_at",  # created_at, upvotes, submitted_at
    order: str = "desc",  # asc, desc
) -> dict:
    """
    Get community deals with pagination and filtering.
    - is_verified: Filter by verification status
    - sort_by: Sort by created_at, upvotes, or submitted_at
    - order: asc or desc
    """
    filters = {"is_deleted": False}
    
    if is_verified is not None:
        filters["is_verified"] = is_verified

    # Validate sort parameters
    valid_sort_fields = ["created_at", "upvotes", "submitted_at"]
    if sort_by not in valid_sort_fields:
        raise BadRequestException(f"Invalid sort_by field. Must be one of: {valid_sort_fields}")
    
    if order not in ["asc", "desc"]:
        raise BadRequestException("Invalid order. Must be 'asc' or 'desc'")

    deals_data = await crud_deals.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        schema_to_select=DealRead,
        **filters,
    )

    response: dict[str, Any] = paginated_response(crud_data=deals_data, page=page, items_per_page=items_per_page)
    return response


@router.post("/deals/", response_model=DealRead, status_code=201)
async def create_deal(
    request: Request,
    deal: DealCreate,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> DealRead:
    """Submit a new deal."""
    # Check if product exists
    db_product = await crud_products.get(db=db, id=deal.product_id, is_deleted=False)
    if db_product is None:
        raise NotFoundException("Product not found")

    # Create deal
    deal_create = DealCreateInternal(
        user_id=current_user["id"],
        **deal.model_dump()
    )
    
    created_deal = await crud_deals.create(db=db, object=deal_create)
    
    deal_read = await crud_deals.get(
        db=db, 
        id=created_deal.id, 
        schema_to_select=DealRead
    )
    if deal_read is None:
        raise NotFoundException("Created deal not found")

    return cast(DealRead, deal_read)


@router.get("/deals/{deal_id}", response_model=DealRead)
async def read_deal(
    request: Request,
    deal_id: int,
    db: Annotated[AsyncSession, Depends(async_get_db)]
) -> DealRead:
    """Get a specific deal by ID."""
    db_deal = await crud_deals.get(
        db=db, 
        id=deal_id, 
        is_deleted=False, 
        schema_to_select=DealRead
    )
    if db_deal is None:
        raise NotFoundException("Deal not found")

    return cast(DealRead, db_deal)


@router.put("/deals/{deal_id}", response_model=dict[str, str])
async def update_deal(
    request: Request,
    deal_id: int,
    values: DealUpdate,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Update a deal (only by the deal creator)."""
    # Check if deal exists
    db_deal = await crud_deals.get(
        db=db, 
        id=deal_id, 
        is_deleted=False, 
        schema_to_select=DealRead
    )
    if db_deal is None:
        raise NotFoundException("Deal not found")

    db_deal = cast(DealRead, db_deal)
    
    # Check if user owns the deal
    if db_deal.user_id != current_user["id"]:
        raise ForbiddenException("You can only edit your own deals")

    # Update deal
    await crud_deals.update(db=db, object=values, id=deal_id)
    return {"message": "Deal updated successfully"}


@router.delete("/deals/{deal_id}")
async def delete_deal(
    request: Request,
    deal_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Delete a deal (only by the deal creator or admin)."""
    # Check if deal exists
    db_deal = await crud_deals.get(
        db=db, 
        id=deal_id, 
        is_deleted=False, 
        schema_to_select=DealRead
    )
    if db_deal is None:
        raise NotFoundException("Deal not found")

    db_deal = cast(DealRead, db_deal)
    
    # Check if user owns the deal or is superuser
    if db_deal.user_id != current_user["id"] and not current_user.get("is_superuser", False):
        raise ForbiddenException("You can only delete your own deals")

    # Soft delete the deal
    await crud_deals.delete(db=db, id=deal_id)
    
    return {"message": "Deal deleted successfully"}


@router.put("/deals/{deal_id}/vote", response_model=dict[str, str])
async def vote_on_deal(
    request: Request,
    deal_id: int,
    vote: DealVote,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Vote on a deal (upvote/downvote)."""
    # Check if deal exists
    db_deal = await crud_deals.get(
        db=db, 
        id=deal_id, 
        is_deleted=False, 
        schema_to_select=DealRead
    )
    if db_deal is None:
        raise NotFoundException("Deal not found")

    db_deal = cast(DealRead, db_deal)
    
    # Users cannot vote on their own deals
    if db_deal.user_id == current_user["id"]:
        raise ForbiddenException("You cannot vote on your own deals")

    # Update vote count
    current_upvotes = db_deal.upvotes
    
    if vote.vote_type == "upvote":
        new_upvotes = current_upvotes + 1
        message = "Deal upvoted successfully"
    else:  # downvote
        new_upvotes = max(0, current_upvotes - 1)  # Don't go below 0
        message = "Deal downvoted successfully"
    
    # Update the deal
    update_data = DealUpdate(upvotes=new_upvotes)
    await crud_deals.update(db=db, object={"upvotes": new_upvotes}, id=deal_id)
    
    return {"message": message, "new_upvotes": new_upvotes}


@router.patch("/deals/{deal_id}/verify", dependencies=[Depends(get_current_superuser)])
async def verify_deal(
    request: Request,
    deal_id: int,
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Verify a deal (admin only)."""
    # Check if deal exists
    db_deal = await crud_deals.get(
        db=db, 
        id=deal_id, 
        is_deleted=False, 
        schema_to_select=DealRead
    )
    if db_deal is None:
        raise NotFoundException("Deal not found")

    # Update verification status
    update_data = DealUpdate(is_verified=True)
    await crud_deals.update(db=db, object=update_data, id=deal_id)
    
    return {"message": "Deal verified successfully"}


@router.get("/deals/user/{user_id}", response_model=PaginatedListResponse[DealRead])
async def read_user_deals(
    request: Request,
    user_id: int,
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 10,
) -> dict:
    """Get deals submitted by a specific user."""
    deals_data = await crud_deals.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        user_id=user_id,
        is_deleted=False,
        schema_to_select=DealRead,
    )

    response: dict[str, Any] = paginated_response(crud_data=deals_data, page=page, items_per_page=items_per_page)
    return response

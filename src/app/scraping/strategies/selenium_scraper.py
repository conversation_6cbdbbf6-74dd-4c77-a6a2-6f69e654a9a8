"""Selenium-based scraper for JavaScript-heavy pages."""

import asyncio
from typing import List, Optional
from urllib.parse import quote

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import TimeoutException, WebDriverException

from ..base_scraper import BaseScraper, ProductData, ScrapingConfig
from ..ai_agent import ai_parsing_agent
from ..exceptions import (
    InvalidURLException,
    ProductNotFoundException,
    NetworkTimeoutException,
    CaptchaDetectedException,
    RateLimitedException
)
from ..utils.anti_detection import anti_detection_manager
from ..utils.proxy_manager import proxy_manager
from ..utils.user_agents import user_agent_manager


class SeleniumScraper(BaseScraper):
    """Selenium-based scraper for JavaScript-heavy pages."""
    
    def __init__(self, config: Optional[ScrapingConfig] = None, browser: str = "chrome"):
        super().__init__(config)
        self.browser = browser.lower()
        self.driver: Optional[webdriver.Remote] = None
        self.wait: Optional[WebDriverWait] = None
    
    async def initialize(self) -> None:
        """Initialize the Selenium WebDriver."""
        try:
            if self.browser == "chrome":
                self.driver = self._create_chrome_driver()
            elif self.browser == "firefox":
                self.driver = self._create_firefox_driver()
            else:
                raise ValueError(f"Unsupported browser: {self.browser}")
            
            self.wait = WebDriverWait(self.driver, self.config.timeout)
            
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Selenium driver: {str(e)}")
    
    async def cleanup(self) -> None:
        """Cleanup the WebDriver."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass
            self.driver = None
            self.wait = None
    
    def _create_chrome_driver(self) -> webdriver.Chrome:
        """Create Chrome WebDriver with anti-detection options."""
        options = ChromeOptions()
        
        # Basic options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        
        # Anti-detection options
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # User agent
        user_agent = user_agent_manager.get_random_user_agent()
        options.add_argument(f"--user-agent={user_agent}")
        
        # Window size
        options.add_argument("--window-size=1920,1080")
        
        # Proxy configuration
        proxy = proxy_manager.get_proxy_config()
        if proxy:
            options.add_argument(f"--proxy-server={proxy}")
        
        # Headless mode (optional)
        # options.add_argument("--headless")
        
        driver = webdriver.Chrome(options=options)
        
        # Execute script to hide automation
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    
    def _create_firefox_driver(self) -> webdriver.Firefox:
        """Create Firefox WebDriver with anti-detection options."""
        options = FirefoxOptions()
        
        # Basic options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        # User agent
        user_agent = user_agent_manager.get_random_user_agent()
        options.set_preference("general.useragent.override", user_agent)
        
        # Proxy configuration
        proxy = proxy_manager.get_proxy_config()
        if proxy:
            # Parse proxy URL
            from urllib.parse import urlparse
            parsed = urlparse(proxy)
            options.set_preference("network.proxy.type", 1)
            options.set_preference("network.proxy.http", parsed.hostname)
            options.set_preference("network.proxy.http_port", parsed.port)
            options.set_preference("network.proxy.ssl", parsed.hostname)
            options.set_preference("network.proxy.ssl_port", parsed.port)
        
        # Headless mode (optional)
        # options.add_argument("--headless")
        
        return webdriver.Firefox(options=options)
    
    async def scrape_product(self, url: str) -> ProductData:
        """
        Scrape a single product from the given URL.
        
        Args:
            url: Amazon product URL
            
        Returns:
            ProductData: Scraped product information
        """
        if not self.validate_url(url):
            raise InvalidURLException(f"Invalid Amazon India URL: {url}")
        
        if not self.driver:
            raise RuntimeError("Driver not initialized")
        
        # Apply delay before request
        await self.apply_delay()
        
        try:
            # Navigate to URL
            self.driver.get(url)
            
            # Wait for page to load
            await self._wait_for_page_load()
            
            # Check for CAPTCHA or blocking
            page_source = self.driver.page_source
            if anti_detection_manager.detect_captcha(page_source):
                await anti_detection_manager.handle_detection('captcha')
                raise CaptchaDetectedException("CAPTCHA detected")
            
            if anti_detection_manager.detect_blocking(page_source, 200):
                await anti_detection_manager.handle_detection('blocking')
                raise RateLimitedException("Request blocking detected")
            
            # Scroll to load lazy content
            await self._scroll_page()
            
            # Get final page source
            html_content = self.driver.page_source
            
            # Parse with AI agent
            product_data = await ai_parsing_agent.parse_product_page(html_content, url)
            
            # Validate required fields
            if not product_data.amazon_asin:
                product_data.amazon_asin = self.extract_asin(url) or ""
            
            if not product_data.title:
                raise ProductNotFoundException("Could not extract product title")
            
            return product_data
            
        except TimeoutException:
            raise NetworkTimeoutException("Page load timeout")
        except WebDriverException as e:
            raise NetworkTimeoutException(f"WebDriver error: {str(e)}")
        except Exception as e:
            raise ProductNotFoundException(f"Failed to parse product data: {str(e)}")
    
    async def scrape_search_results(self, query: str, max_results: int = 10) -> List[ProductData]:
        """
        Scrape products from search results.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List[ProductData]: List of scraped products
        """
        if not self.driver:
            raise RuntimeError("Driver not initialized")
        
        search_url = self._build_search_url(query)
        
        # Apply delay before request
        await self.apply_delay()
        
        try:
            # Navigate to search URL
            self.driver.get(search_url)
            
            # Wait for search results to load
            await self._wait_for_search_results()
            
            # Scroll to load more results
            await self._scroll_page()
            
            # Extract product URLs
            product_urls = self._extract_product_urls_from_page()
            
            # Limit results
            product_urls = product_urls[:max_results]
            
            # Scrape individual products
            products = []
            for product_url in product_urls:
                try:
                    # Apply delay between product scrapes
                    await self.apply_delay()
                    
                    product_data = await self.scrape_product(product_url)
                    products.append(product_data)
                    
                except Exception as e:
                    # Continue with other products if one fails
                    print(f"Failed to scrape product {product_url}: {str(e)}")
                    continue
            
            return products
            
        except TimeoutException:
            raise NetworkTimeoutException("Search page load timeout")
        except WebDriverException as e:
            raise NetworkTimeoutException(f"WebDriver error: {str(e)}")
    
    async def _wait_for_page_load(self) -> None:
        """Wait for page to load completely."""
        try:
            # Wait for basic page elements
            self.wait.until(
                EC.any_of(
                    EC.presence_of_element_located((By.ID, "productTitle")),
                    EC.presence_of_element_located((By.CLASS_NAME, "product-title")),
                    EC.presence_of_element_located((By.TAG_NAME, "h1"))
                )
            )
            
            # Additional wait for dynamic content
            await asyncio.sleep(2)
            
        except TimeoutException:
            # Continue anyway, might still be able to parse
            pass
    
    async def _wait_for_search_results(self) -> None:
        """Wait for search results to load."""
        try:
            self.wait.until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-component-type='s-search-result']")),
                    EC.presence_of_element_located((By.CLASS_NAME, "s-result-item")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".s-main-slot .s-result-item"))
                )
            )
            
            # Additional wait for dynamic content
            await asyncio.sleep(2)
            
        except TimeoutException:
            # Continue anyway
            pass
    
    async def _scroll_page(self) -> None:
        """Scroll page to load lazy content."""
        try:
            # Scroll down gradually
            for i in range(3):
                self.driver.execute_script(f"window.scrollTo(0, {(i + 1) * 500});")
                await asyncio.sleep(1)
            
            # Scroll back to top
            self.driver.execute_script("window.scrollTo(0, 0);")
            await asyncio.sleep(1)
            
        except Exception:
            # Continue if scrolling fails
            pass
    
    def _build_search_url(self, query: str) -> str:
        """Build Amazon search URL."""
        base_url = "https://www.amazon.in/s"
        encoded_query = quote(query)
        return f"{base_url}?k={encoded_query}&ref=sr_pg_1"
    
    def _extract_product_urls_from_page(self) -> List[str]:
        """Extract product URLs from current page."""
        product_urls = []
        
        try:
            # Find product links
            selectors = [
                "h2.a-size-mini a",
                ".s-link-style a",
                "[data-component-type='s-search-result'] h2 a",
                ".s-result-item h2 a"
            ]
            
            for selector in selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    href = element.get_attribute('href')
                    if href and self.validate_url(href) and href not in product_urls:
                        product_urls.append(href)
                
                # Break if we found products with this selector
                if product_urls:
                    break
            
        except Exception:
            # Continue if extraction fails
            pass
        
        return product_urls

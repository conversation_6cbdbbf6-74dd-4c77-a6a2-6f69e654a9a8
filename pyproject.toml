[project]
name = "fastapi-boilerplate"
version = "0.1.0"
description = "A fully Async FastAPI boilerplate using SQLAlchemy and Pydantic 2"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
license = { text = "MIT" }
readme = "README.md"
requires-python = "~=3.11"
dependencies = [
    "python-dotenv>=1.0.0",
    "pydantic[email]>=2.6.1",
    "fastapi>=0.109.1",
    "uvicorn>=0.27.0",
    "uvloop>=0.19.0",
    "httptools>=0.6.1",
    "uuid>=1.30",
    "alembic>=1.13.1",
    "asyncpg>=0.29.0",
    "SQLAlchemy-Utils>=0.41.1",
    "python-jose>=3.3.0",
    "SQLAlchemy>=2.0.25",
    "python-multipart>=0.0.9",
    "greenlet>=2.0.2",
    "httpx>=0.26.0",
    "pydantic-settings>=2.0.3",
    "redis>=5.0.1",
    "arq>=0.25.0",
    "bcrypt>=4.1.1",
    "psycopg2-binary>=2.9.9",
    "fastcrud>=0.15.5",
    "crudadmin>=0.4.2",
    "gunicorn>=23.0.0",
    "ruff>=0.11.13",
    "mypy>=1.16.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.2",
    "pytest-mock>=3.14.0",
    "faker>=26.0.0",
    "mypy>=1.8.0",
    "types-redis>=4.6.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.sdist]
include = ["src/"]

[tool.hatch.build.targets.wheel]
include = ["src/"]
packages = ["src"]

[tool.ruff]
target-version = "py311"
line-length = 120
fix = true

[tool.ruff.lint]
select = [
    # https://docs.astral.sh/ruff/rules/#pyflakes-f
    "F", # Pyflakes
    # https://docs.astral.sh/ruff/rules/#pycodestyle-e-w
    "E", # pycodestyle
    "W", # Warning
    # https://docs.astral.sh/ruff/rules/#flake8-comprehensions-c4
    # https://docs.astral.sh/ruff/rules/#mccabe-c90
    "C", # Complexity (mccabe+) & comprehensions
    # https://docs.astral.sh/ruff/rules/#pyupgrade-up
    "UP", # pyupgrade
    # https://docs.astral.sh/ruff/rules/#isort-i
    "I", # isort
]
ignore = [
    # https://docs.astral.sh/ruff/rules/#pycodestyle-e-w
    "E402", # module level import not at top of file
    # https://docs.astral.sh/ruff/rules/#pyupgrade-up
    "UP006", # use-pep585-annotation
    "UP007", # use-pep604-annotation
    "E741",  # Ambiguous variable name
    # "UP035", # deprecated-assertion
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = [
    "F401", # unused import
    "F403", # star imports
]

[tool.ruff.lint.mccabe]
max-complexity = 24

[tool.ruff.lint.pydocstyle]
convention = "numpy"

[tool.pytest.ini_options]
filterwarnings = [
    "ignore::PendingDeprecationWarning:starlette.formparsers",
]

[dependency-groups]
dev = [
    "pytest-asyncio>=1.0.0",
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true
mypy_path = "src"
explicit_package_bases = true

[[tool.mypy.overrides]]
module = "src.app.*"
disallow_untyped_defs = true

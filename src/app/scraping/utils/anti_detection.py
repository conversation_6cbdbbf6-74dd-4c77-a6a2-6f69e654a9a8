"""Anti-detection utilities for web scraping."""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any

from .user_agents import user_agent_manager
from .proxy_manager import proxy_manager


class AntiDetectionManager:
    """Manages anti-detection measures for scraping."""
    
    def __init__(self):
        self.request_count = 0
        self.last_request_time = 0
        self.session_start_time = time.time()
        
    async def apply_request_delay(self, min_delay: float = 1.0, max_delay: float = 3.0) -> None:
        """
        Apply random delay between requests.
        
        Args:
            min_delay: Minimum delay in seconds
            max_delay: Maximum delay in seconds
        """
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
        
    def should_rotate_user_agent(self) -> bool:
        """
        Determine if user agent should be rotated.
        
        Returns:
            bool: True if user agent should be rotated
        """
        # Rotate every 5-10 requests
        return self.request_count > 0 and self.request_count % random.randint(5, 10) == 0
        
    def should_rotate_proxy(self) -> bool:
        """
        Determine if proxy should be rotated.
        
        Returns:
            bool: True if proxy should be rotated
        """
        # Rotate every 10-20 requests
        return self.request_count > 0 and self.request_count % random.randint(10, 20) == 0
        
    def get_adaptive_headers(self, url: str = None) -> Dict[str, str]:
        """
        Get adaptive headers based on request patterns.
        
        Args:
            url: Target URL for context-specific headers
            
        Returns:
            dict: Headers dictionary
        """
        # Rotate user agent if needed
        if self.should_rotate_user_agent():
            user_agent_manager.rotate_user_agent()
            
        headers = user_agent_manager.get_headers()
        
        # Add randomized headers
        if random.choice([True, False]):
            headers['Accept-Language'] = random.choice([
                'en-US,en;q=0.9,hi;q=0.8',
                'en-US,en;q=0.9',
                'en-GB,en;q=0.9,hi;q=0.8',
                'hi-IN,hi;q=0.9,en;q=0.8'
            ])
            
        # Randomly include or exclude some headers
        optional_headers = {
            'DNT': '1',
            'Sec-GPC': '1',
            'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': random.choice(['"Windows"', '"macOS"', '"Linux"']),
        }
        
        for header, value in optional_headers.items():
            if random.choice([True, False]):
                headers[header] = value
                
        return headers
        
    def get_session_config(self) -> Dict[str, Any]:
        """
        Get session configuration with anti-detection measures.
        
        Returns:
            dict: Session configuration
        """
        config = {
            'headers': self.get_adaptive_headers(),
            'timeout': random.uniform(10, 30),
        }
        
        # Add proxy if enabled and available
        proxy = proxy_manager.get_proxy_config()
        if proxy:
            config['proxy'] = proxy
            
        return config
        
    def detect_captcha(self, html_content: str) -> bool:
        """
        Detect if CAPTCHA is present in the HTML content.
        
        Args:
            html_content: HTML content to analyze
            
        Returns:
            bool: True if CAPTCHA detected
        """
        captcha_indicators = [
            'captcha',
            'robot',
            'verify',
            'security check',
            'unusual traffic',
            'automated requests',
            'solve this puzzle',
            'prove you\'re human',
            'recaptcha',
            'hcaptcha'
        ]
        
        html_lower = html_content.lower()
        return any(indicator in html_lower for indicator in captcha_indicators)
        
    def detect_rate_limiting(self, html_content: str, status_code: int) -> bool:
        """
        Detect if rate limiting is in effect.
        
        Args:
            html_content: HTML content to analyze
            status_code: HTTP status code
            
        Returns:
            bool: True if rate limiting detected
        """
        if status_code in [429, 503, 504]:
            return True
            
        rate_limit_indicators = [
            'too many requests',
            'rate limit',
            'slow down',
            'try again later',
            'temporarily unavailable',
            'service unavailable'
        ]
        
        html_lower = html_content.lower()
        return any(indicator in html_lower for indicator in rate_limit_indicators)
        
    def detect_blocking(self, html_content: str, status_code: int) -> bool:
        """
        Detect if request is being blocked.
        
        Args:
            html_content: HTML content to analyze
            status_code: HTTP status code
            
        Returns:
            bool: True if blocking detected
        """
        if status_code in [403, 406, 451]:
            return True
            
        blocking_indicators = [
            'access denied',
            'blocked',
            'forbidden',
            'not allowed',
            'unauthorized',
            'your request has been blocked',
            'this request is not allowed'
        ]
        
        html_lower = html_content.lower()
        return any(indicator in html_lower for indicator in blocking_indicators)
        
    async def handle_detection(self, detection_type: str) -> None:
        """
        Handle different types of detection.
        
        Args:
            detection_type: Type of detection (captcha, rate_limit, blocking)
        """
        if detection_type == 'captcha':
            # Longer delay and rotate everything
            await asyncio.sleep(random.uniform(30, 60))
            user_agent_manager.rotate_user_agent()
            proxy_manager.rotate_proxy()
            
        elif detection_type == 'rate_limit':
            # Moderate delay and rotate proxy
            await asyncio.sleep(random.uniform(10, 30))
            proxy_manager.rotate_proxy()
            
        elif detection_type == 'blocking':
            # Rotate proxy and user agent
            user_agent_manager.rotate_user_agent()
            proxy_manager.rotate_proxy()
            await asyncio.sleep(random.uniform(5, 15))
            
    def calculate_adaptive_delay(self) -> float:
        """
        Calculate adaptive delay based on request patterns.
        
        Returns:
            float: Delay in seconds
        """
        base_delay = random.uniform(1.0, 3.0)
        
        # Increase delay based on request frequency
        if self.request_count > 50:
            base_delay *= 1.5
        elif self.request_count > 100:
            base_delay *= 2.0
            
        # Add random variation
        variation = random.uniform(0.5, 1.5)
        return base_delay * variation
        
    def update_request_stats(self) -> None:
        """Update request statistics."""
        self.request_count += 1
        self.last_request_time = time.time()
        
        # Rotate proxy if needed
        if self.should_rotate_proxy():
            proxy_manager.rotate_proxy()
            
    def get_stats(self) -> Dict[str, Any]:
        """
        Get anti-detection statistics.
        
        Returns:
            dict: Statistics about anti-detection measures
        """
        current_time = time.time()
        session_duration = current_time - self.session_start_time
        
        return {
            'request_count': self.request_count,
            'session_duration': session_duration,
            'requests_per_minute': (self.request_count / session_duration) * 60 if session_duration > 0 else 0,
            'last_request_time': self.last_request_time,
            'current_user_agent': user_agent_manager.get_current_user_agent(),
            'current_proxy': proxy_manager.get_current_proxy(),
        }
        
    def reset_session(self) -> None:
        """Reset session statistics."""
        self.request_count = 0
        self.session_start_time = time.time()
        self.last_request_time = 0
        user_agent_manager.rotate_user_agent()
        proxy_manager.rotate_proxy()


# Global instance for easy access
anti_detection_manager = AntiDetectionManager()

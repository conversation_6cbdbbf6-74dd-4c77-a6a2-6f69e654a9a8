from fastcrud import FastCRUD

from ..models.user_favorite import UserFavorite
from ..schemas.user_favorite import UserFavoriteCreateInternal, UserFavoriteRead, UserFavoriteUpdate, UserFavoriteUpdateInternal, UserFavoriteDelete

CRUDUserFavorite = FastCRUD[UserFavorite, UserFavoriteCreateInternal, UserFavoriteRead, UserFavoriteUpdate, UserFavoriteUpdateInternal, UserFavoriteDelete]
crud_user_favorites = CRUDUserFavorite(UserFavorite)

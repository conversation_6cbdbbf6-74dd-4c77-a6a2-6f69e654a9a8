import uuid as uuid_pkg
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum

from sqlalchemy import DateTime, JSON, Numeric, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.db.database import Base


class AvailabilityStatus(Enum):
    IN_STOCK = "in_stock"
    OUT_OF_STOCK = "out_of_stock"
    LIMITED = "limited"


class Product(Base):
    __tablename__ = "product"

    id: Mapped[int] = mapped_column("id", autoincrement=True, nullable=False, unique=True, primary_key=True, init=False)
    
    # Amazon specific fields
    amazon_asin: Mapped[str] = mapped_column(String(20), unique=True, index=True)
    
    # Product information
    title: Mapped[str] = mapped_column(Text, index=True)
    description: Mapped[str | None] = mapped_column(Text, default=None)
    category: Mapped[str | None] = mapped_column(String(100), index=True, default=None)
    brand: Mapped[str | None] = mapped_column(String(100), index=True, default=None)
    
    # Images stored as JSON array of URLs
    images: Mapped[list[str] | None] = mapped_column(JSON, default=None)
    
    # Pricing information
    current_price: Mapped[Decimal | None] = mapped_column(Numeric(10, 2), default=None)
    original_price: Mapped[Decimal | None] = mapped_column(Numeric(10, 2), default=None)
    discount_percentage: Mapped[int | None] = mapped_column(default=None)
    
    # Availability and ratings
    availability_status: Mapped[AvailabilityStatus] = mapped_column(default=AvailabilityStatus.IN_STOCK)
    average_rating: Mapped[Decimal | None] = mapped_column(Numeric(3, 2), default=None)
    total_reviews: Mapped[int | None] = mapped_column(default=None)
    
    # Timestamps
    last_scraped: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    
    # Standard fields
    uuid: Mapped[uuid_pkg.UUID] = mapped_column(default_factory=uuid_pkg.uuid4, unique=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default_factory=lambda: datetime.now(UTC))
    updated_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    deleted_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    is_deleted: Mapped[bool] = mapped_column(default=False, index=True)

    # Relationships
    price_history: Mapped[list["PriceHistory"]] = relationship("PriceHistory", back_populates="product", init=False)
    product_alerts: Mapped[list["ProductAlert"]] = relationship("ProductAlert", back_populates="product", init=False)
    user_favorites: Mapped[list["UserFavorite"]] = relationship("UserFavorite", back_populates="product", init=False)
    deals: Mapped[list["Deal"]] = relationship("Deal", back_populates="product", init=False)

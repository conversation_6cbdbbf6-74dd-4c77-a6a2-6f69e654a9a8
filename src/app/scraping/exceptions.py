"""Custom exceptions for the scraping system."""


class ScrapingException(Exception):
    """Base exception for scraping errors."""
    pass


class InvalidURLException(ScrapingException):
    """Raised when the provided URL is invalid or not supported."""
    pass


class ProductNotFoundException(ScrapingException):
    """Raised when the product is not found on the page."""
    pass


class CaptchaDetectedException(ScrapingException):
    """Raised when a CAPTCHA is detected on the page."""
    pass


class RateLimitedException(ScrapingException):
    """Raised when rate limiting is detected."""
    pass


class NetworkTimeoutException(ScrapingException):
    """Raised when network requests timeout."""
    pass


class ParsingException(ScrapingException):
    """Raised when HTML parsing fails."""
    pass


class AntiDetectionException(ScrapingException):
    """Raised when anti-detection measures fail."""
    pass


class AIParsingException(ScrapingException):
    """Raised when AI-powered parsing fails."""
    pass


class ProxyException(ScrapingException):
    """Raised when proxy-related errors occur."""
    pass

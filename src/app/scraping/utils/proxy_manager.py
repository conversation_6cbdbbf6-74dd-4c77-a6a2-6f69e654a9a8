"""Proxy management for web scraping."""

import asyncio
import random
from typing import List, Optional, Dict, Any
from urllib.parse import urlparse

import aiohttp

from ...core.config import settings
from ..exceptions import ProxyException


class ProxyManager:
    """Manages proxy rotation and validation for scraping."""
    
    def __init__(self, proxy_list: List[str] = None):
        """
        Initialize ProxyManager.
        
        Args:
            proxy_list: List of proxy URLs (format: *********************:port or http://host:port)
        """
        self.proxy_list = proxy_list or self._parse_proxy_list()
        self.working_proxies = []
        self.failed_proxies = []
        self.current_proxy = None
        self.rotation_enabled = settings.PROXY_ROTATION
        
    def _parse_proxy_list(self) -> List[str]:
        """
        Parse proxy list from settings.
        
        Returns:
            List[str]: List of proxy URLs
        """
        if not settings.PROXY_LIST:
            return []
            
        # Split by comma or newline
        proxies = []
        for proxy in settings.PROXY_LIST.replace('\n', ',').split(','):
            proxy = proxy.strip()
            if proxy:
                # Ensure proxy has protocol
                if not proxy.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
                    proxy = f"http://{proxy}"
                proxies.append(proxy)
        
        return proxies
    
    async def validate_proxy(self, proxy: str, timeout: int = 10) -> bool:
        """
        Validate if a proxy is working.
        
        Args:
            proxy: Proxy URL to validate
            timeout: Timeout for validation request
            
        Returns:
            bool: True if proxy is working, False otherwise
        """
        try:
            connector = aiohttp.TCPConnector()
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as session:
                async with session.get(
                    'http://httpbin.org/ip',
                    proxy=proxy
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return 'origin' in data
            return False
        except Exception:
            return False
    
    async def validate_all_proxies(self) -> None:
        """Validate all proxies and categorize them."""
        if not self.proxy_list:
            return
            
        tasks = []
        for proxy in self.proxy_list:
            tasks.append(self.validate_proxy(proxy))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        self.working_proxies = []
        self.failed_proxies = []
        
        for proxy, result in zip(self.proxy_list, results):
            if isinstance(result, bool) and result:
                self.working_proxies.append(proxy)
            else:
                self.failed_proxies.append(proxy)
    
    def get_random_proxy(self) -> Optional[str]:
        """
        Get a random working proxy.
        
        Returns:
            str: Random proxy URL or None if no working proxies
        """
        if not self.working_proxies:
            return None
        
        self.current_proxy = random.choice(self.working_proxies)
        return self.current_proxy
    
    def get_current_proxy(self) -> Optional[str]:
        """
        Get the current proxy.
        
        Returns:
            str: Current proxy URL or None
        """
        return self.current_proxy
    
    def rotate_proxy(self) -> Optional[str]:
        """
        Rotate to a new proxy.
        
        Returns:
            str: New proxy URL or None if no working proxies
        """
        if not self.rotation_enabled or not self.working_proxies:
            return self.current_proxy
            
        # Remove current proxy from working list temporarily to avoid immediate reuse
        available_proxies = [p for p in self.working_proxies if p != self.current_proxy]
        
        if not available_proxies:
            # If only one proxy or all failed, use any working proxy
            available_proxies = self.working_proxies
        
        self.current_proxy = random.choice(available_proxies)
        return self.current_proxy
    
    def mark_proxy_failed(self, proxy: str) -> None:
        """
        Mark a proxy as failed and remove it from working proxies.
        
        Args:
            proxy: Proxy URL that failed
        """
        if proxy in self.working_proxies:
            self.working_proxies.remove(proxy)
            self.failed_proxies.append(proxy)
            
        # If current proxy failed, rotate to a new one
        if proxy == self.current_proxy:
            self.rotate_proxy()
    
    def get_proxy_config(self) -> Optional[str]:
        """
        Get proxy configuration for HTTP requests.
        
        Returns:
            str: Proxy URL or None if no proxy should be used
        """
        if not settings.PROXY_ENABLED:
            return None
            
        if not self.current_proxy and self.working_proxies:
            return self.get_random_proxy()
            
        return self.current_proxy
    
    def get_proxy_dict(self) -> Optional[Dict[str, str]]:
        """
        Get proxy configuration as dictionary for requests library.
        
        Returns:
            dict: Proxy configuration or None
        """
        proxy = self.get_proxy_config()
        if not proxy:
            return None
            
        return {
            'http': proxy,
            'https': proxy
        }
    
    async def test_proxy_with_amazon(self, proxy: str) -> bool:
        """
        Test proxy specifically with Amazon India.
        
        Args:
            proxy: Proxy URL to test
            
        Returns:
            bool: True if proxy works with Amazon, False otherwise
        """
        try:
            connector = aiohttp.TCPConnector()
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=15)
            ) as session:
                async with session.get(
                    'https://www.amazon.in/',
                    proxy=proxy,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    }
                ) as response:
                    return response.status == 200 and 'amazon' in (await response.text()).lower()
        except Exception:
            return False
    
    async def validate_proxies_with_amazon(self) -> None:
        """Validate all proxies specifically with Amazon India."""
        if not self.proxy_list:
            return
            
        tasks = []
        for proxy in self.proxy_list:
            tasks.append(self.test_proxy_with_amazon(proxy))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        self.working_proxies = []
        self.failed_proxies = []
        
        for proxy, result in zip(self.proxy_list, results):
            if isinstance(result, bool) and result:
                self.working_proxies.append(proxy)
            else:
                self.failed_proxies.append(proxy)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get proxy manager statistics.
        
        Returns:
            dict: Statistics about proxy usage
        """
        return {
            'total_proxies': len(self.proxy_list),
            'working_proxies': len(self.working_proxies),
            'failed_proxies': len(self.failed_proxies),
            'current_proxy': self.current_proxy,
            'rotation_enabled': self.rotation_enabled,
            'proxy_enabled': settings.PROXY_ENABLED
        }


# Global instance for easy access
proxy_manager = ProxyManager()

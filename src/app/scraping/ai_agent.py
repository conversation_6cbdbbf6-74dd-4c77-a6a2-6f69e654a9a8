"""AI-powered HTML parsing and content recognition for Amazon products."""

import json
import re
from decimal import Decimal
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup

from ..core.config import settings
from .base_scraper import ProductData
from .exceptions import AIParsingException


class AIParsingAgent:
    """AI agent for intelligent HTML parsing and content recognition."""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize AI API clients."""
        try:
            if settings.OPENAI_API_KEY:
                import openai
                self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        except ImportError:
            pass
            
        try:
            if settings.ANTHROPIC_API_KEY:
                import anthropic
                self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.ANTHROPIC_API_KEY)
        except ImportError:
            pass
    
    async def parse_product_page(self, html_content: str, url: str) -> ProductData:
        """
        Parse Amazon product page using AI.
        
        Args:
            html_content: Raw HTML content
            url: Product URL
            
        Returns:
            ProductData: Parsed product information
            
        Raises:
            AIParsingException: If AI parsing fails
        """
        # First try to extract basic info using traditional parsing
        soup = BeautifulSoup(html_content, 'html.parser')
        basic_data = self._extract_basic_data(soup, url)
        
        # Use AI to enhance and validate the extraction
        if self.openai_client or self.anthropic_client:
            try:
                ai_data = await self._ai_parse_content(html_content, url)
                # Merge AI results with basic extraction
                enhanced_data = self._merge_parsing_results(basic_data, ai_data)
                return enhanced_data
            except Exception as e:
                # Fall back to basic parsing if AI fails
                if basic_data.amazon_asin and basic_data.title:
                    return basic_data
                raise AIParsingException(f"AI parsing failed and basic parsing insufficient: {str(e)}")
        
        # Return basic data if no AI available
        if basic_data.amazon_asin and basic_data.title:
            return basic_data
        
        raise AIParsingException("No AI client available and basic parsing insufficient")
    
    def _extract_basic_data(self, soup: BeautifulSoup, url: str) -> ProductData:
        """
        Extract basic product data using traditional parsing.
        
        Args:
            soup: BeautifulSoup object
            url: Product URL
            
        Returns:
            ProductData: Basic product information
        """
        # Extract ASIN from URL
        asin = self._extract_asin_from_url(url)
        
        # Extract title
        title = self._extract_title(soup)
        
        # Extract prices
        current_price, original_price = self._extract_prices(soup)
        
        # Extract other basic info
        description = self._extract_description(soup)
        brand = self._extract_brand(soup)
        category = self._extract_category(soup)
        images = self._extract_images(soup)
        rating, reviews = self._extract_rating_and_reviews(soup)
        availability = self._extract_availability(soup)
        
        # Calculate discount
        discount_percentage = None
        if original_price and current_price and original_price > current_price:
            discount_percentage = int(((original_price - current_price) / original_price) * 100)
        
        return ProductData(
            amazon_asin=asin or "",
            title=title or "",
            description=description,
            brand=brand,
            category=category,
            images=images,
            current_price=current_price,
            original_price=original_price,
            discount_percentage=discount_percentage,
            availability_status=availability,
            average_rating=rating,
            total_reviews=reviews
        )
    
    def _extract_asin_from_url(self, url: str) -> Optional[str]:
        """Extract ASIN from URL."""
        patterns = [
            r'/dp/([A-Z0-9]{10})',
            r'/gp/product/([A-Z0-9]{10})',
            r'asin=([A-Z0-9]{10})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract product title."""
        selectors = [
            '#productTitle',
            '.product-title',
            'h1.a-size-large',
            'h1[data-automation-id="product-title"]',
            '.pdp-product-name'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        return None
    
    def _extract_prices(self, soup: BeautifulSoup) -> tuple[Optional[Decimal], Optional[Decimal]]:
        """Extract current and original prices."""
        current_price = None
        original_price = None
        
        # Current price selectors
        current_price_selectors = [
            '.a-price-whole',
            '.a-offscreen',
            '.a-price .a-offscreen',
            '#price_inside_buybox',
            '.a-price-range .a-offscreen'
        ]
        
        for selector in current_price_selectors:
            element = soup.select_one(selector)
            if element:
                price_text = element.get_text(strip=True)
                current_price = self._parse_price(price_text)
                if current_price:
                    break
        
        # Original price selectors
        original_price_selectors = [
            '.a-text-price .a-offscreen',
            '.a-price.a-text-price .a-offscreen',
            '.a-price-was .a-offscreen'
        ]
        
        for selector in original_price_selectors:
            element = soup.select_one(selector)
            if element:
                price_text = element.get_text(strip=True)
                original_price = self._parse_price(price_text)
                if original_price:
                    break
        
        return current_price, original_price
    
    def _parse_price(self, price_text: str) -> Optional[Decimal]:
        """Parse price from text."""
        if not price_text:
            return None
        
        # Remove currency symbols and clean
        cleaned = re.sub(r'[₹,\s]', '', price_text)
        
        # Extract numeric value
        match = re.search(r'(\d+\.?\d*)', cleaned)
        if match:
            try:
                return Decimal(match.group(1))
            except:
                pass
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract product description."""
        selectors = [
            '#feature-bullets ul',
            '.a-unordered-list.a-vertical',
            '#productDescription',
            '.product-description'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)[:1000]  # Limit length
        return None
    
    def _extract_brand(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract brand name."""
        selectors = [
            '#bylineInfo',
            '.a-link-normal[data-attribute="brand"]',
            '.brand'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                brand = element.get_text(strip=True)
                # Clean brand text
                brand = re.sub(r'^(Brand:|Visit the|Store:|by)\s*', '', brand, flags=re.IGNORECASE)
                return brand.strip()
        return None
    
    def _extract_category(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract product category."""
        # Try breadcrumb navigation
        breadcrumb = soup.select_one('#wayfinding-breadcrumbs_feature_div')
        if breadcrumb:
            links = breadcrumb.select('a')
            if len(links) > 1:
                return links[-2].get_text(strip=True)  # Second to last is usually the category
        
        return None
    
    def _extract_images(self, soup: BeautifulSoup) -> Optional[List[str]]:
        """Extract product images."""
        images = []
        
        # Main product image
        main_img = soup.select_one('#landingImage, #imgBlkFront')
        if main_img and main_img.get('src'):
            images.append(main_img['src'])
        
        # Additional images
        img_elements = soup.select('.a-dynamic-image, .item-image img')
        for img in img_elements:
            src = img.get('src') or img.get('data-src')
            if src and src not in images:
                images.append(src)
        
        return images if images else None
    
    def _extract_rating_and_reviews(self, soup: BeautifulSoup) -> tuple[Optional[Decimal], Optional[int]]:
        """Extract rating and review count."""
        rating = None
        reviews = None
        
        # Rating
        rating_element = soup.select_one('.a-icon-alt, [data-hook="average-star-rating"]')
        if rating_element:
            rating_text = rating_element.get_text(strip=True)
            rating_match = re.search(r'(\d+\.?\d*)', rating_text)
            if rating_match:
                try:
                    rating = Decimal(rating_match.group(1))
                except:
                    pass
        
        # Review count
        review_element = soup.select_one('#acrCustomerReviewText, [data-hook="total-review-count"]')
        if review_element:
            review_text = review_element.get_text(strip=True)
            review_match = re.search(r'(\d+)', review_text.replace(',', ''))
            if review_match:
                try:
                    reviews = int(review_match.group(1))
                except:
                    pass
        
        return rating, reviews
    
    def _extract_availability(self, soup: BeautifulSoup) -> str:
        """Extract availability status."""
        availability_element = soup.select_one('#availability span, .a-color-success, .a-color-state')
        if availability_element:
            availability_text = availability_element.get_text(strip=True).lower()
            if 'in stock' in availability_text or 'available' in availability_text:
                return 'in_stock'
            elif 'out of stock' in availability_text or 'unavailable' in availability_text:
                return 'out_of_stock'
            elif 'limited' in availability_text:
                return 'limited'
        
        return 'in_stock'  # Default assumption

    async def _ai_parse_content(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Use AI to parse HTML content.

        Args:
            html_content: Raw HTML content
            url: Product URL

        Returns:
            dict: Parsed product data
        """
        # Clean and truncate HTML for AI processing
        cleaned_html = self._clean_html_for_ai(html_content)

        if self.openai_client:
            return await self._openai_parse(cleaned_html, url)
        elif self.anthropic_client:
            return await self._anthropic_parse(cleaned_html, url)
        else:
            raise AIParsingException("No AI client available")

    def _clean_html_for_ai(self, html_content: str) -> str:
        """
        Clean and truncate HTML content for AI processing.

        Args:
            html_content: Raw HTML content

        Returns:
            str: Cleaned HTML content
        """
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove script and style elements
        for script in soup(["script", "style", "noscript"]):
            script.decompose()

        # Focus on main content areas
        main_content = soup.select_one('#dp-container, #ppd, .s-main-slot')
        if main_content:
            soup = main_content

        # Get text content with some structure
        text_content = soup.get_text(separator='\n', strip=True)

        # Truncate to reasonable length for AI processing
        max_length = 8000  # Adjust based on AI model limits
        if len(text_content) > max_length:
            text_content = text_content[:max_length] + "..."

        return text_content

    async def _openai_parse(self, content: str, url: str) -> Dict[str, Any]:
        """Parse content using OpenAI."""
        prompt = self._create_parsing_prompt(content, url)

        try:
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting product information from Amazon product pages."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )

            result_text = response.choices[0].message.content
            return self._parse_ai_response(result_text)

        except Exception as e:
            raise AIParsingException(f"OpenAI parsing failed: {str(e)}")

    async def _anthropic_parse(self, content: str, url: str) -> Dict[str, Any]:
        """Parse content using Anthropic Claude."""
        prompt = self._create_parsing_prompt(content, url)

        try:
            response = await self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                temperature=0.1,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )

            result_text = response.content[0].text
            return self._parse_ai_response(result_text)

        except Exception as e:
            raise AIParsingException(f"Anthropic parsing failed: {str(e)}")

    def _create_parsing_prompt(self, content: str, url: str) -> str:
        """Create prompt for AI parsing."""
        return f"""
Extract product information from this Amazon India product page content and return it as JSON.

URL: {url}

Content:
{content}

Please extract the following information and return as valid JSON:
{{
    "title": "Product title",
    "brand": "Brand name",
    "category": "Product category",
    "current_price": "Current price as number (without currency)",
    "original_price": "Original price as number (without currency, if different from current)",
    "discount_percentage": "Discount percentage as integer",
    "description": "Brief product description",
    "availability": "in_stock, out_of_stock, or limited",
    "rating": "Average rating as decimal",
    "review_count": "Number of reviews as integer",
    "images": ["Array of image URLs"]
}}

Rules:
- Return only valid JSON
- Use null for missing values
- Prices should be numbers without currency symbols
- Extract the main product title, not variations
- Focus on the primary product being sold
"""

    def _parse_ai_response(self, response_text: str) -> Dict[str, Any]:
        """Parse AI response into structured data."""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # Try to parse the entire response as JSON
                return json.loads(response_text)
        except json.JSONDecodeError as e:
            raise AIParsingException(f"Failed to parse AI response as JSON: {str(e)}")

    def _merge_parsing_results(self, basic_data: ProductData, ai_data: Dict[str, Any]) -> ProductData:
        """
        Merge basic parsing results with AI parsing results.

        Args:
            basic_data: Basic parsing results
            ai_data: AI parsing results

        Returns:
            ProductData: Merged results
        """
        # Use AI data where available and valid, fall back to basic data
        return ProductData(
            amazon_asin=basic_data.amazon_asin or "",
            title=ai_data.get('title') or basic_data.title or "",
            description=ai_data.get('description') or basic_data.description,
            brand=ai_data.get('brand') or basic_data.brand,
            category=ai_data.get('category') or basic_data.category,
            images=ai_data.get('images') or basic_data.images,
            current_price=self._safe_decimal(ai_data.get('current_price')) or basic_data.current_price,
            original_price=self._safe_decimal(ai_data.get('original_price')) or basic_data.original_price,
            discount_percentage=ai_data.get('discount_percentage') or basic_data.discount_percentage,
            availability_status=ai_data.get('availability') or basic_data.availability_status,
            average_rating=self._safe_decimal(ai_data.get('rating')) or basic_data.average_rating,
            total_reviews=ai_data.get('review_count') or basic_data.total_reviews
        )

    def _safe_decimal(self, value: Any) -> Optional[Decimal]:
        """Safely convert value to Decimal."""
        if value is None:
            return None
        try:
            return Decimal(str(value))
        except:
            return None


# Global instance for easy access
ai_parsing_agent = AIParsingAgent()

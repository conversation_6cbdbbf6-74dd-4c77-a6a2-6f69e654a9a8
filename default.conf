# ---------------- Running With One Server ----------------
server {
    listen 80;

    location / {
        proxy_pass http://web:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}


# # ---------------- To Run with Multiple Servers, Uncomment below ----------------
# upstream fastapi_app {
#     server fastapi1:8000;  # Replace with actual server names or IP addresses
#     server fastapi2:8000;
#     # Add more servers as needed
# }

# server {
#     listen 80;

#     location / {
#         proxy_pass http://fastapi_app;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
# }

import uuid as uuid_pkg
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum

from sqlalchemy import DateTime, ForeignKey, Numeric
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.db.database import Base


class AlertType(Enum):
    EMAIL = "email"
    SMS = "sms"
    BOTH = "both"


class ProductAlert(Base):
    __tablename__ = "product_alert"

    id: Mapped[int] = mapped_column("id", autoincrement=True, nullable=False, unique=True, primary_key=True, init=False)
    
    # Foreign keys
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    product_id: Mapped[int] = mapped_column(ForeignKey("product.id"), index=True)
    
    # Alert configuration
    target_price: Mapped[Decimal] = mapped_column(Numeric(10, 2))
    alert_type: Mapped[AlertType] = mapped_column(default=AlertType.EMAIL)
    is_active: Mapped[bool] = mapped_column(default=True, index=True)
    
    # Standard fields
    uuid: Mapped[uuid_pkg.UUID] = mapped_column(default_factory=uuid_pkg.uuid4, unique=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default_factory=lambda: datetime.now(UTC))
    updated_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    deleted_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    is_deleted: Mapped[bool] = mapped_column(default=False, index=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="product_alerts", init=False)
    product: Mapped["Product"] = relationship("Product", back_populates="product_alerts", init=False)

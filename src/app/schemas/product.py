from datetime import datetime
from decimal import Decimal
from typing import Annotated
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, HttpUrl

from ..models.product import AvailabilityStatus


class ProductBase(BaseModel):
    amazon_asin: Annotated[str, Field(min_length=10, max_length=20, examples=["B08N5WRWNW"])]
    title: Annotated[str, Field(min_length=1, max_length=1000, examples=["Apple iPhone 13 (128GB) - Blue"])]
    description: Annotated[str | None, Field(default=None, max_length=5000)]
    category: Annotated[str | None, Field(default=None, max_length=100, examples=["Electronics"])]
    brand: Annotated[str | None, Field(default=None, max_length=100, examples=["Apple"])]
    images: Annotated[list[str] | None, Field(default=None, examples=[["https://example.com/image1.jpg"]])]
    current_price: Annotated[Decimal | None, Field(default=None, ge=0, examples=[69900.00])]
    original_price: Annotated[Decimal | None, Field(default=None, ge=0, examples=[79900.00])]
    discount_percentage: Annotated[int | None, Field(default=None, ge=0, le=100, examples=[12])]
    availability_status: AvailabilityStatus = AvailabilityStatus.IN_STOCK
    average_rating: Annotated[Decimal | None, Field(default=None, ge=0, le=5, examples=[4.5])]
    total_reviews: Annotated[int | None, Field(default=None, ge=0, examples=[1234])]


class Product(ProductBase):
    model_config = ConfigDict(from_attributes=True)


class ProductRead(ProductBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    uuid: UUID
    last_scraped: datetime | None
    created_at: datetime
    updated_at: datetime | None
    is_deleted: bool


class ProductCreate(ProductBase):
    pass


class ProductCreateInternal(ProductCreate):
    pass


class ProductUpdate(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    title: Annotated[str | None, Field(default=None, min_length=1, max_length=1000)]
    description: Annotated[str | None, Field(default=None, max_length=5000)]
    category: Annotated[str | None, Field(default=None, max_length=100)]
    brand: Annotated[str | None, Field(default=None, max_length=100)]
    images: Annotated[list[str] | None, Field(default=None)]
    current_price: Annotated[Decimal | None, Field(default=None, ge=0)]
    original_price: Annotated[Decimal | None, Field(default=None, ge=0)]
    discount_percentage: Annotated[int | None, Field(default=None, ge=0, le=100)]
    availability_status: AvailabilityStatus | None = None
    average_rating: Annotated[Decimal | None, Field(default=None, ge=0, le=5)]
    total_reviews: Annotated[int | None, Field(default=None, ge=0)]
    last_scraped: datetime | None = None


class ProductUpdateInternal(ProductUpdate):
    updated_at: datetime


class ProductDelete(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    is_deleted: bool
    deleted_at: datetime


# Schema for scraping requests
class ProductScrapeRequest(BaseModel):
    url: Annotated[str, Field(examples=["https://www.amazon.in/dp/B08N5WRWNW"])]
    force_refresh: bool = False


# Schema for search requests
class ProductSearchRequest(BaseModel):
    query: Annotated[str, Field(min_length=1, max_length=200, examples=["iPhone 13"])]
    max_results: Annotated[int, Field(default=10, ge=1, le=50)]

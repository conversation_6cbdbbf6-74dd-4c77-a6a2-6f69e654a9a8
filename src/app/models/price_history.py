import uuid as uuid_pkg
from datetime import UTC, datetime
from decimal import Decimal

from sqlalchemy import DateTime, ForeignKey, Numeric
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.db.database import Base
from .product import AvailabilityStatus


class PriceHistory(Base):
    __tablename__ = "price_history"

    id: Mapped[int] = mapped_column("id", autoincrement=True, nullable=False, unique=True, primary_key=True, init=False)
    
    # Foreign key to Product
    product_id: Mapped[int] = mapped_column(ForeignKey("product.id"), index=True)
    
    # Price information
    original_price: Mapped[Decimal | None] = mapped_column(Numeric(10, 2), default=None)
    discount_price: Mapped[Decimal | None] = mapped_column(Numeric(10, 2), default=None)
    discount_percentage: Mapped[int | None] = mapped_column(default=None)
    
    # Availability at the time of scraping
    availability_status: Mapped[AvailabilityStatus] = mapped_column(default=AvailabilityStatus.IN_STOCK)
    
    # When this price was scraped
    scraped_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default_factory=lambda: datetime.now(UTC))
    
    # Standard fields
    uuid: Mapped[uuid_pkg.UUID] = mapped_column(default_factory=uuid_pkg.uuid4, unique=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default_factory=lambda: datetime.now(UTC))
    
    # Relationship to Product
    product: Mapped["Product"] = relationship("Product", back_populates="price_history", init=False)

"""Abstract base class for Amazon product scrapers."""

import asyncio
import random
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse

from ..core.config import settings
from .exceptions import InvalidURLException, NetworkTimeoutException


@dataclass
class ProductData:
    """Data structure for scraped product information."""
    amazon_asin: str
    title: str
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    images: Optional[List[str]] = None
    current_price: Optional[Decimal] = None
    original_price: Optional[Decimal] = None
    discount_percentage: Optional[int] = None
    availability_status: str = "in_stock"
    average_rating: Optional[Decimal] = None
    total_reviews: Optional[int] = None
    scraped_at: datetime = None
    
    def __post_init__(self):
        if self.scraped_at is None:
            self.scraped_at = datetime.utcnow()


@dataclass
class ScrapingConfig:
    """Configuration for scraping operations."""
    delay_min: int = settings.SCRAPING_DELAY_MIN
    delay_max: int = settings.SCRAPING_DELAY_MAX
    timeout: int = settings.SCRAPING_TIMEOUT
    retries: int = settings.SCRAPING_RETRIES
    user_agent_rotation: bool = settings.SCRAPING_USER_AGENT_ROTATION
    proxy_enabled: bool = settings.PROXY_ENABLED
    respect_robots: bool = settings.AMAZON_RESPECT_ROBOTS


class BaseScraper(ABC):
    """Abstract base class for Amazon product scrapers."""
    
    def __init__(self, config: Optional[ScrapingConfig] = None):
        self.config = config or ScrapingConfig()
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the scraper (setup session, browser, etc.)."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources (close session, browser, etc.)."""
        pass
    
    @abstractmethod
    async def scrape_product(self, url: str) -> ProductData:
        """
        Scrape a single product from the given URL.
        
        Args:
            url: Amazon product URL
            
        Returns:
            ProductData: Scraped product information
            
        Raises:
            InvalidURLException: If URL is invalid
            ProductNotFoundException: If product not found
            NetworkTimeoutException: If request times out
            ScrapingException: For other scraping errors
        """
        pass
    
    @abstractmethod
    async def scrape_search_results(self, query: str, max_results: int = 10) -> List[ProductData]:
        """
        Scrape products from search results.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List[ProductData]: List of scraped products
        """
        pass
    
    def validate_url(self, url: str) -> bool:
        """
        Validate if the URL is a valid Amazon India product URL.
        
        Args:
            url: URL to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            parsed = urlparse(url)
            return (
                parsed.netloc in ['www.amazon.in', 'amazon.in'] and
                ('/dp/' in parsed.path or '/gp/product/' in parsed.path)
            )
        except Exception:
            return False
    
    def extract_asin(self, url: str) -> Optional[str]:
        """
        Extract ASIN from Amazon URL.
        
        Args:
            url: Amazon product URL
            
        Returns:
            str: ASIN if found, None otherwise
        """
        try:
            if '/dp/' in url:
                return url.split('/dp/')[1].split('/')[0].split('?')[0]
            elif '/gp/product/' in url:
                return url.split('/gp/product/')[1].split('/')[0].split('?')[0]
            return None
        except Exception:
            return None
    
    async def apply_delay(self) -> None:
        """Apply random delay between requests."""
        delay = random.uniform(self.config.delay_min, self.config.delay_max)
        await asyncio.sleep(delay)
    
    def clean_price(self, price_text: str) -> Optional[Decimal]:
        """
        Clean and convert price text to Decimal.
        
        Args:
            price_text: Raw price text from webpage
            
        Returns:
            Decimal: Cleaned price or None if parsing fails
        """
        if not price_text:
            return None
            
        try:
            # Remove currency symbols, commas, and extra spaces
            cleaned = price_text.replace('₹', '').replace(',', '').strip()
            # Extract numeric part
            import re
            price_match = re.search(r'[\d.]+', cleaned)
            if price_match:
                return Decimal(price_match.group())
            return None
        except Exception:
            return None
    
    def clean_text(self, text: str) -> str:
        """
        Clean text by removing extra whitespace and special characters.
        
        Args:
            text: Raw text
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
        return ' '.join(text.strip().split())
    
    def calculate_discount_percentage(self, original_price: Optional[Decimal], 
                                    current_price: Optional[Decimal]) -> Optional[int]:
        """
        Calculate discount percentage.
        
        Args:
            original_price: Original price
            current_price: Current price
            
        Returns:
            int: Discount percentage or None
        """
        if not original_price or not current_price or original_price <= current_price:
            return None
            
        try:
            discount = ((original_price - current_price) / original_price) * 100
            return int(round(discount))
        except Exception:
            return None

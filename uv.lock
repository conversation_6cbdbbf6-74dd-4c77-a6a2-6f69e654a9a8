# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o uv.lock
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.14
    # via fastapi-boilerplate (pyproject.toml)
aiosignal==1.4.0
    # via aiohttp
aiosqlite==0.21.0
    # via crudadmin
alembic==1.16.4
    # via fastapi-boilerplate (pyproject.toml)
annotated-types==0.7.0
    # via pydantic
anthropic==0.57.1
    # via fastapi-boilerplate (pyproject.toml)
anyio==4.9.0
    # via
    #   anthropic
    #   httpx
    #   openai
    #   starlette
arq==0.25.0
    # via fastapi-boilerplate (pyproject.toml)
asyncpg==0.30.0
    # via fastapi-boilerplate (pyproject.toml)
attrs==25.3.0
    # via
    #   aiohttp
    #   outcome
    #   trio
bcrypt==4.3.0
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
beautifulsoup4==4.13.4
    # via fastapi-boilerplate (pyproject.toml)
certifi==2025.7.9
    # via
    #   httpcore
    #   httpx
    #   selenium
click==8.2.1
    # via
    #   arq
    #   uvicorn
crudadmin==0.4.2
    # via fastapi-boilerplate (pyproject.toml)
distro==1.9.0
    # via
    #   anthropic
    #   openai
dnspython==2.7.0
    # via email-validator
ecdsa==0.19.1
    # via python-jose
email-validator==2.2.0
    # via pydantic
fastapi==0.116.1
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
    #   fastcrud
fastcrud==0.15.12
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
greenlet==3.2.3
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
    #   playwright
gunicorn==23.0.0
    # via fastapi-boilerplate (pyproject.toml)
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
    #   wsproto
hiredis==3.2.1
    # via redis
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via fastapi-boilerplate (pyproject.toml)
httpx==0.28.1
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   anthropic
    #   openai
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   trio
    #   yarl
jinja2==3.1.6
    # via crudadmin
jiter==0.10.0
    # via
    #   anthropic
    #   openai
mako==1.3.10
    # via alembic
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
multidict==6.6.3
    # via
    #   aiohttp
    #   yarl
mypy==1.16.1
    # via fastapi-boilerplate (pyproject.toml)
mypy-extensions==1.1.0
    # via mypy
openai==1.95.0
    # via fastapi-boilerplate (pyproject.toml)
outcome==1.3.0.post0
    # via
    #   trio
    #   trio-websocket
packaging==25.0
    # via gunicorn
pathspec==0.12.1
    # via mypy
playwright==1.53.0
    # via fastapi-boilerplate (pyproject.toml)
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
psycopg2-binary==2.9.10
    # via fastapi-boilerplate (pyproject.toml)
pyasn1==0.6.1
    # via
    #   python-jose
    #   rsa
pydantic==2.11.7
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   anthropic
    #   crudadmin
    #   fastapi
    #   fastcrud
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
pyee==13.0.0
    # via playwright
pysocks==1.7.1
    # via urllib3
python-dotenv==1.1.1
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   pydantic-settings
python-jose==3.5.0
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
python-multipart==0.0.20
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   crudadmin
redis==6.2.0
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   arq
rsa==4.9.1
    # via python-jose
ruff==0.12.3
    # via fastapi-boilerplate (pyproject.toml)
selenium==4.34.2
    # via fastapi-boilerplate (pyproject.toml)
six==1.17.0
    # via ecdsa
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
    #   trio
sortedcontainers==2.4.0
    # via trio
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==2.0.41
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   alembic
    #   crudadmin
    #   fastcrud
    #   sqlalchemy-utils
sqlalchemy-utils==0.41.2
    # via
    #   fastapi-boilerplate (pyproject.toml)
    #   fastcrud
starlette==0.47.1
    # via fastapi
tqdm==4.67.1
    # via openai
trio==0.30.0
    # via
    #   selenium
    #   trio-websocket
trio-websocket==0.12.2
    # via selenium
typing-extensions==4.14.1
    # via
    #   aiosqlite
    #   alembic
    #   anthropic
    #   arq
    #   beautifulsoup4
    #   fastapi
    #   mypy
    #   openai
    #   pydantic
    #   pydantic-core
    #   pyee
    #   selenium
    #   sqlalchemy
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
ua-parser==1.0.1
    # via user-agents
ua-parser-builtins==0.18.0.post1
    # via ua-parser
urllib3==2.5.0
    # via selenium
user-agents==2.2.0
    # via crudadmin
uuid==1.30
    # via fastapi-boilerplate (pyproject.toml)
uvicorn==0.35.0
    # via fastapi-boilerplate (pyproject.toml)
uvloop==0.21.0
    # via fastapi-boilerplate (pyproject.toml)
websocket-client==1.8.0
    # via selenium
wsproto==1.2.0
    # via trio-websocket
yarl==1.20.1
    # via aiohttp

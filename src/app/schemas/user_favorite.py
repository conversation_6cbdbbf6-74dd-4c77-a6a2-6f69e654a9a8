from datetime import datetime
from typing import Annotated
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class UserFavoriteBase(BaseModel):
    product_id: int


class UserFavorite(UserFavoriteBase):
    model_config = ConfigDict(from_attributes=True)


class UserFavoriteRead(UserFavoriteBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    uuid: UUID
    user_id: int
    created_at: datetime
    is_deleted: bool


class UserFavoriteCreate(UserFavoriteBase):
    pass


class UserFavoriteCreateInternal(UserFavoriteCreate):
    user_id: int


class UserFavoriteUpdate(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    # No fields to update for favorites - it's either added or removed


class UserFavoriteUpdateInternal(UserFavoriteUpdate):
    pass


class UserFavoriteDelete(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    is_deleted: bool
    deleted_at: datetime

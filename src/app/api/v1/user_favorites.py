from typing import Annotated, Any, cast

from fastapi import APIRouter, Depends, Request
from fastcrud.paginated import PaginatedListResponse, compute_offset, paginated_response
from sqlalchemy.ext.asyncio import AsyncSession

from ...api.dependencies import get_current_user
from ...core.db.database import async_get_db
from ...core.exceptions.http_exceptions import NotFoundException, DuplicateValueException
from ...crud.crud_user_favorites import crud_user_favorites
from ...crud.crud_products import crud_products
from ...schemas.user_favorite import UserFavoriteRead, UserFavoriteCreate, UserFavoriteCreateInternal
from ...schemas.product import ProductRead

router = APIRouter(tags=["user-favorites"])


@router.get("/users/favorites", response_model=PaginatedListResponse[UserFavoriteRead])
async def read_user_favorites(
    request: Request,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 10,
) -> dict:
    """Get current user's favorite products."""
    favorites_data = await crud_user_favorites.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        user_id=current_user["id"],
        is_deleted=False,
        schema_to_select=UserFavoriteRead,
    )

    response: dict[str, Any] = paginated_response(crud_data=favorites_data, page=page, items_per_page=items_per_page)
    return response


@router.get("/users/favorites/products", response_model=PaginatedListResponse[ProductRead])
async def read_user_favorite_products(
    request: Request,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
    page: int = 1,
    items_per_page: int = 10,
) -> dict:
    """Get current user's favorite products with full product details."""
    # Get user's favorites
    favorites_data = await crud_user_favorites.get_multi(
        db=db,
        user_id=current_user["id"],
        is_deleted=False,
    )
    
    if not favorites_data["data"]:
        return paginated_response(crud_data={"data": [], "total_count": 0}, page=page, items_per_page=items_per_page)
    
    # Extract product IDs from favorites
    product_ids = [fav["product_id"] for fav in favorites_data["data"]]
    
    # Get products with pagination
    products_data = await crud_products.get_multi(
        db=db,
        offset=compute_offset(page, items_per_page),
        limit=items_per_page,
        id__in=product_ids,
        is_deleted=False,
        schema_to_select=ProductRead,
    )

    response: dict[str, Any] = paginated_response(crud_data=products_data, page=page, items_per_page=items_per_page)
    return response


@router.post("/users/favorites/{product_id}", response_model=UserFavoriteRead, status_code=201)
async def add_product_to_favorites(
    request: Request,
    product_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> UserFavoriteRead:
    """Add a product to current user's favorites."""
    # Check if product exists
    db_product = await crud_products.get(db=db, id=product_id, is_deleted=False)
    if db_product is None:
        raise NotFoundException("Product not found")

    # Check if already in favorites
    existing_favorite = await crud_user_favorites.get(
        db=db, 
        user_id=current_user["id"], 
        product_id=product_id,
        is_deleted=False
    )
    if existing_favorite:
        raise DuplicateValueException("Product is already in favorites")

    # Create favorite
    favorite_create = UserFavoriteCreateInternal(
        user_id=current_user["id"],
        product_id=product_id
    )
    
    created_favorite = await crud_user_favorites.create(db=db, object=favorite_create)
    
    favorite_read = await crud_user_favorites.get(
        db=db, 
        id=created_favorite.id, 
        schema_to_select=UserFavoriteRead
    )
    if favorite_read is None:
        raise NotFoundException("Created favorite not found")

    return cast(UserFavoriteRead, favorite_read)


@router.delete("/users/favorites/{product_id}")
async def remove_product_from_favorites(
    request: Request,
    product_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, str]:
    """Remove a product from current user's favorites."""
    # Check if favorite exists
    db_favorite = await crud_user_favorites.get(
        db=db,
        user_id=current_user["id"],
        product_id=product_id,
        is_deleted=False
    )
    if db_favorite is None:
        raise NotFoundException("Product not found in favorites")

    # Soft delete the favorite
    await crud_user_favorites.delete(
        db=db,
        user_id=current_user["id"],
        product_id=product_id
    )
    
    return {"message": "Product removed from favorites"}


@router.get("/users/favorites/{product_id}/check")
async def check_product_in_favorites(
    request: Request,
    product_id: int,
    current_user: Annotated[dict, Depends(get_current_user)],
    db: Annotated[AsyncSession, Depends(async_get_db)],
) -> dict[str, bool]:
    """Check if a product is in current user's favorites."""
    favorite = await crud_user_favorites.get(
        db=db,
        user_id=current_user["id"],
        product_id=product_id,
        is_deleted=False
    )
    
    return {"is_favorite": favorite is not None}

"""Add Amazon scraper models

Revision ID: 001_amazon_scraper
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_amazon_scraper'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create product table
    op.create_table('product',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('amazon_asin', sa.String(length=20), nullable=False),
    sa.Column('title', sa.Text(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('brand', sa.String(length=100), nullable=True),
    sa.Column('images', sa.JSON(), nullable=True),
    sa.Column('current_price', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('original_price', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('discount_percentage', sa.Integer(), nullable=True),
    sa.Column('availability_status', sa.Enum('IN_STOCK', 'OUT_OF_STOCK', 'LIMITED', name='availabilitystatus'), nullable=False),
    sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('total_reviews', sa.Integer(), nullable=True),
    sa.Column('last_scraped', sa.DateTime(timezone=True), nullable=True),
    sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('amazon_asin'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_product_brand'), 'product', ['brand'], unique=False)
    op.create_index(op.f('ix_product_category'), 'product', ['category'], unique=False)
    op.create_index(op.f('ix_product_is_deleted'), 'product', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_product_title'), 'product', ['title'], unique=False)

    # Create price_history table
    op.create_table('price_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('original_price', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('discount_price', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('discount_percentage', sa.Integer(), nullable=True),
    sa.Column('availability_status', sa.Enum('IN_STOCK', 'OUT_OF_STOCK', 'LIMITED', name='availabilitystatus'), nullable=False),
    sa.Column('scraped_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_price_history_product_id'), 'price_history', ['product_id'], unique=False)

    # Create product_alert table
    op.create_table('product_alert',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('target_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('alert_type', sa.Enum('EMAIL', 'SMS', 'BOTH', name='alerttype'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_product_alert_is_active'), 'product_alert', ['is_active'], unique=False)
    op.create_index(op.f('ix_product_alert_is_deleted'), 'product_alert', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_product_alert_product_id'), 'product_alert', ['product_id'], unique=False)
    op.create_index(op.f('ix_product_alert_user_id'), 'product_alert', ['user_id'], unique=False)

    # Create user_favorite table
    op.create_table('user_favorite',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'product_id', name='unique_user_product_favorite'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_user_favorite_is_deleted'), 'user_favorite', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_user_favorite_product_id'), 'user_favorite', ['product_id'], unique=False)
    op.create_index(op.f('ix_user_favorite_user_id'), 'user_favorite', ['user_id'], unique=False)

    # Create deal table
    op.create_table('deal',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('deal_title', sa.String(length=200), nullable=False),
    sa.Column('deal_description', sa.Text(), nullable=True),
    sa.Column('deal_url', sa.String(length=500), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('upvotes', sa.Integer(), nullable=False),
    sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_deal_is_deleted'), 'deal', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_deal_is_verified'), 'deal', ['is_verified'], unique=False)
    op.create_index(op.f('ix_deal_product_id'), 'deal', ['product_id'], unique=False)
    op.create_index(op.f('ix_deal_user_id'), 'deal', ['user_id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_deal_user_id'), table_name='deal')
    op.drop_index(op.f('ix_deal_product_id'), table_name='deal')
    op.drop_index(op.f('ix_deal_is_verified'), table_name='deal')
    op.drop_index(op.f('ix_deal_is_deleted'), table_name='deal')
    op.drop_table('deal')
    
    op.drop_index(op.f('ix_user_favorite_user_id'), table_name='user_favorite')
    op.drop_index(op.f('ix_user_favorite_product_id'), table_name='user_favorite')
    op.drop_index(op.f('ix_user_favorite_is_deleted'), table_name='user_favorite')
    op.drop_table('user_favorite')
    
    op.drop_index(op.f('ix_product_alert_user_id'), table_name='product_alert')
    op.drop_index(op.f('ix_product_alert_product_id'), table_name='product_alert')
    op.drop_index(op.f('ix_product_alert_is_deleted'), table_name='product_alert')
    op.drop_index(op.f('ix_product_alert_is_active'), table_name='product_alert')
    op.drop_table('product_alert')
    
    op.drop_index(op.f('ix_price_history_product_id'), table_name='price_history')
    op.drop_table('price_history')
    
    op.drop_index(op.f('ix_product_title'), table_name='product')
    op.drop_index(op.f('ix_product_is_deleted'), table_name='product')
    op.drop_index(op.f('ix_product_category'), table_name='product')
    op.drop_index(op.f('ix_product_brand'), table_name='product')
    op.drop_table('product')
    
    # Drop enums
    sa.Enum(name='alerttype').drop(op.get_bind())
    sa.Enum(name='availabilitystatus').drop(op.get_bind())

"""Playwright-based scraper for modern web automation."""

import asyncio
from typing import List, Optional
from urllib.parse import quote

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, TimeoutError as PlaywrightTimeoutError

from ..base_scraper import Base<PERSON><PERSON>raper, ProductData, ScrapingConfig
from ..ai_agent import ai_parsing_agent
from ..exceptions import (
    InvalidURLException,
    ProductNotFoundException,
    NetworkTimeoutException,
    CaptchaDetectedException,
    RateLimitedException
)
from ..utils.anti_detection import anti_detection_manager
from ..utils.proxy_manager import proxy_manager
from ..utils.user_agents import user_agent_manager


class PlaywrightScraper(BaseScraper):
    """Playwright-based scraper for modern web automation."""
    
    def __init__(self, config: Optional[ScrapingConfig] = None, browser_type: str = "chromium"):
        super().__init__(config)
        self.browser_type = browser_type.lower()
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
    
    async def initialize(self) -> None:
        """Initialize Playwright browser."""
        try:
            self.playwright = await async_playwright().start()
            
            # Browser launch options
            launch_options = {
                "headless": True,  # Set to False for debugging
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-blink-features=AutomationControlled"
                ]
            }
            
            # Proxy configuration
            proxy = proxy_manager.get_proxy_config()
            if proxy:
                from urllib.parse import urlparse
                parsed = urlparse(proxy)
                launch_options["proxy"] = {
                    "server": f"{parsed.scheme}://{parsed.hostname}:{parsed.port}"
                }
                if parsed.username and parsed.password:
                    launch_options["proxy"]["username"] = parsed.username
                    launch_options["proxy"]["password"] = parsed.password
            
            # Launch browser
            if self.browser_type == "chromium":
                self.browser = await self.playwright.chromium.launch(**launch_options)
            elif self.browser_type == "firefox":
                self.browser = await self.playwright.firefox.launch(**launch_options)
            elif self.browser_type == "webkit":
                self.browser = await self.playwright.webkit.launch(**launch_options)
            else:
                raise ValueError(f"Unsupported browser type: {self.browser_type}")
            
            # Create context with anti-detection measures
            context_options = {
                "user_agent": user_agent_manager.get_random_user_agent(),
                "viewport": {"width": 1920, "height": 1080},
                "locale": "en-US",
                "timezone_id": "Asia/Kolkata",
                "permissions": [],
                "extra_http_headers": {
                    "Accept-Language": "en-US,en;q=0.9,hi;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "DNT": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "none",
                    "Sec-Fetch-User": "?1",
                    "Upgrade-Insecure-Requests": "1"
                }
            }
            
            self.context = await self.browser.new_context(**context_options)
            
            # Add stealth scripts
            await self.context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en', 'hi'],
                });
                
                window.chrome = {
                    runtime: {},
                };
            """)
            
            # Create page
            self.page = await self.context.new_page()
            
            # Set timeout
            self.page.set_default_timeout(self.config.timeout * 1000)
            
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Playwright: {str(e)}")
    
    async def cleanup(self) -> None:
        """Cleanup Playwright resources."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception:
            pass
        
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
    
    async def scrape_product(self, url: str) -> ProductData:
        """
        Scrape a single product from the given URL.
        
        Args:
            url: Amazon product URL
            
        Returns:
            ProductData: Scraped product information
        """
        if not self.validate_url(url):
            raise InvalidURLException(f"Invalid Amazon India URL: {url}")
        
        if not self.page:
            raise RuntimeError("Playwright not initialized")
        
        # Apply delay before request
        await self.apply_delay()
        
        try:
            # Navigate to URL
            response = await self.page.goto(url, wait_until="domcontentloaded")
            
            if response and response.status >= 400:
                if response.status == 429:
                    raise RateLimitedException("Rate limited by server")
                elif response.status in [403, 406]:
                    await anti_detection_manager.handle_detection('blocking')
                    raise RateLimitedException("Request blocked by server")
            
            # Wait for page to load
            await self._wait_for_page_load()
            
            # Check for CAPTCHA or blocking
            page_content = await self.page.content()
            if anti_detection_manager.detect_captcha(page_content):
                await anti_detection_manager.handle_detection('captcha')
                raise CaptchaDetectedException("CAPTCHA detected")
            
            if anti_detection_manager.detect_blocking(page_content, response.status if response else 200):
                await anti_detection_manager.handle_detection('blocking')
                raise RateLimitedException("Request blocking detected")
            
            # Scroll to load lazy content
            await self._scroll_page()
            
            # Get final page content
            html_content = await self.page.content()
            
            # Parse with AI agent
            product_data = await ai_parsing_agent.parse_product_page(html_content, url)
            
            # Validate required fields
            if not product_data.amazon_asin:
                product_data.amazon_asin = self.extract_asin(url) or ""
            
            if not product_data.title:
                raise ProductNotFoundException("Could not extract product title")
            
            return product_data
            
        except PlaywrightTimeoutError:
            raise NetworkTimeoutException("Page load timeout")
        except Exception as e:
            if "CAPTCHA" in str(e) or "captcha" in str(e):
                raise CaptchaDetectedException(str(e))
            raise ProductNotFoundException(f"Failed to parse product data: {str(e)}")
    
    async def scrape_search_results(self, query: str, max_results: int = 10) -> List[ProductData]:
        """
        Scrape products from search results.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List[ProductData]: List of scraped products
        """
        if not self.page:
            raise RuntimeError("Playwright not initialized")
        
        search_url = self._build_search_url(query)
        
        # Apply delay before request
        await self.apply_delay()
        
        try:
            # Navigate to search URL
            await self.page.goto(search_url, wait_until="domcontentloaded")
            
            # Wait for search results to load
            await self._wait_for_search_results()
            
            # Scroll to load more results
            await self._scroll_page()
            
            # Extract product URLs
            product_urls = await self._extract_product_urls_from_page()
            
            # Limit results
            product_urls = product_urls[:max_results]
            
            # Scrape individual products
            products = []
            for product_url in product_urls:
                try:
                    # Apply delay between product scrapes
                    await self.apply_delay()
                    
                    product_data = await self.scrape_product(product_url)
                    products.append(product_data)
                    
                except Exception as e:
                    # Continue with other products if one fails
                    print(f"Failed to scrape product {product_url}: {str(e)}")
                    continue
            
            return products
            
        except PlaywrightTimeoutError:
            raise NetworkTimeoutException("Search page load timeout")
        except Exception as e:
            raise NetworkTimeoutException(f"Playwright error: {str(e)}")
    
    async def _wait_for_page_load(self) -> None:
        """Wait for page to load completely."""
        try:
            # Wait for product title or main content
            await self.page.wait_for_selector(
                "#productTitle, .product-title, h1",
                timeout=10000
            )
            
            # Additional wait for dynamic content
            await asyncio.sleep(2)
            
        except PlaywrightTimeoutError:
            # Continue anyway, might still be able to parse
            pass
    
    async def _wait_for_search_results(self) -> None:
        """Wait for search results to load."""
        try:
            await self.page.wait_for_selector(
                "[data-component-type='s-search-result'], .s-result-item",
                timeout=10000
            )
            
            # Additional wait for dynamic content
            await asyncio.sleep(2)
            
        except PlaywrightTimeoutError:
            # Continue anyway
            pass
    
    async def _scroll_page(self) -> None:
        """Scroll page to load lazy content."""
        try:
            # Scroll down gradually
            for i in range(3):
                await self.page.evaluate(f"window.scrollTo(0, {(i + 1) * 500})")
                await asyncio.sleep(1)
            
            # Scroll back to top
            await self.page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(1)
            
        except Exception:
            # Continue if scrolling fails
            pass
    
    def _build_search_url(self, query: str) -> str:
        """Build Amazon search URL."""
        base_url = "https://www.amazon.in/s"
        encoded_query = quote(query)
        return f"{base_url}?k={encoded_query}&ref=sr_pg_1"
    
    async def _extract_product_urls_from_page(self) -> List[str]:
        """Extract product URLs from current page."""
        product_urls = []
        
        try:
            # Find product links
            selectors = [
                "h2.a-size-mini a",
                ".s-link-style a",
                "[data-component-type='s-search-result'] h2 a",
                ".s-result-item h2 a"
            ]
            
            for selector in selectors:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    href = await element.get_attribute('href')
                    if href:
                        # Convert relative URLs to absolute
                        if href.startswith('/'):
                            href = f"https://www.amazon.in{href}"
                        
                        if self.validate_url(href) and href not in product_urls:
                            product_urls.append(href)
                
                # Break if we found products with this selector
                if product_urls:
                    break
            
        except Exception:
            # Continue if extraction fails
            pass
        
        return product_urls

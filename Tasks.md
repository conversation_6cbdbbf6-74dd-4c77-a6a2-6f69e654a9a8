Create a FastAPI backend project for an Amazon India product scraping application with the following structure:

PROJECT REQUIREMENTS:

FastAPI framework with Pydantic models
PostgreSQL database with SQLAlchemy ORM
JWT authentication system
Environment configuration management
CORS middleware for frontend integration
API versioning structure
FOLDER STRUCTURE:
app/
├── main.py (FastAPI app initialization)
├── config.py (environment variables and settings)
├── dependencies.py (common dependencies and JWT verification)
├── database.py (database connection and session management)
├── models/ (SQLAlchemy database models)
├── schemas/ (Pydantic request/response schemas)
├── routers/ (API route handlers)
├── core/ (authentication and security utilities)
└── utils/ (helper functions)

IMPLEMENTATION DETAILS:

Set up FastAPI app with middleware for CORS and security headers
Configure PostgreSQL connection with connection pooling
Implement JWT token-based authentication with refresh tokens
Create base models for Users, Products, Price_History, Product_Alerts, User_Favorites, Deals
Add environment variable management for database URL, JWT secret, etc.
Include basic health check endpoint at /health
Set up API documentation with proper tags and descriptions
SECURITY REQUIREMENTS:

Password hashing with bcrypt
JWT token expiration and refresh mechanism
Input validation and sanitization
Rate limiting configuration
SQL injection prevention
OUTPUT: Complete FastAPI backend structure with all configuration files, database models, and authentication system ready for development.

Integrate an AI-powered web scraping system into the FastAPI backend for Amazon India product extraction:

SCRAPING REQUIREMENTS:

- Extract: product title, images (URLs), description, reviews, ratings, original price, discount price
- Support multiple scraping strategies: BeautifulSoup (lightweight), Selenium (JavaScript), Playwright (modern)
- AI-powered content recognition for adaptive parsing
- Anti-detection measures: user agent rotation, proxy support, request delays
- Error handling and retry mechanisms

IMPLEMENTATION STRUCTURE:
app/scraping/
├── **init**.py
├── base_scraper.py (abstract base class)
├── ai_agent.py (AI-powered parsing logic)
├── strategies/
│ ├── beautifulsoup_scraper.py
│ ├── selenium_scraper.py
│ └── playwright_scraper.py
├── utils/
│ ├── proxy_manager.py
│ ├── user_agents.py
│ └── anti_detection.py
└── exceptions.py (custom scraping exceptions)

AI FEATURES:

1. Use OpenAI/Anthropic API to intelligently parse HTML content
2. Automatic fallback between scraping strategies if one fails
3. Dynamic selector generation based on page structure
4. Content validation using AI to ensure data quality
5. Adaptive delay calculation based on website response patterns

AMAZON INDIA SPECIFIC:

- Handle Amazon's dynamic loading and lazy loading images
- Extract product ASIN for unique identification
- Parse reviews with sentiment analysis
- Handle multiple price formats (₹ symbol, commas, discount percentages)
- Respect robots.txt and implement rate limiting (1-2 requests per second)

API ENDPOINTS:

- POST /api/v1/scrape/product (scrape single product by URL)
- POST /api/v1/scrape/search (scrape products from search results)
- GET /api/v1/scrape/status/{task_id} (check scraping job status)

ERROR HANDLING:

- Captcha detection and notification
- Network timeout handling
- Invalid URL detection
- Product not found scenarios
- Rate limiting responses

OUTPUT: Complete AI scraping system with multiple strategies, anti-detection measures, and seamless FastAPI integration.

Create comprehensive database models and API endpoints for the Amazon product scraping application:

DATABASE MODELS (SQLAlchemy):

1. User Model:

- user_id (UUID, primary key)
- username (unique, indexed)
- email (unique, indexed)
- password_hash
- is_active (boolean)
- created_at, updated_at (timestamps)

2. Product Model:

- product_id (UUID, primary key)
- amazon_asin (unique, indexed)
- title (text, indexed for search)
- description (text)
- category (varchar, indexed)
- brand (varchar, indexed)
- images (JSON array of URLs)
- current_price (decimal)
- original_price (decimal)
- discount_percentage (integer)
- availability_status (enum: in_stock, out_of_stock, limited)
- average_rating (decimal)
- total_reviews (integer)
- last_scraped (timestamp)
- created_at, updated_at

3. Price_History Model:

- price_id (UUID, primary key)
- product_id (foreign key to Product)
- original_price (decimal)
- discount_price (decimal)
- discount_percentage (integer)
- availability_status
- scraped_at (timestamp)

4. Product_Alert Model:

- alert_id (UUID, primary key)
- user_id (foreign key to User)
- product_id (foreign key to Product)
- target_price (decimal)
- alert_type (enum: email, sms, both)
- is_active (boolean)
- created_at

5. User_Favorites Model:

- favorite_id (UUID, primary key)
- user_id (foreign key)
- product_id (foreign key)
- created_at
- Unique constraint on (user_id, product_id)

6. Deals Model:

- deal_id (UUID, primary key)
- user_id (foreign key)
- product_id (foreign key)
- deal_title (varchar)
- deal_description (text)
- deal_url (varchar)
- submitted_at (timestamp)
- is_verified (boolean)
- upvotes (integer, default 0)

API ENDPOINTS:

AUTHENTICATION:

- POST /api/v1/auth/register
- POST /api/v1/auth/login
- POST /api/v1/auth/refresh
- POST /api/v1/auth/logout

PRODUCTS:

- GET /api/v1/products/ (search with filters: category, brand, price range)
- GET /api/v1/products/{product_id}
- POST /api/v1/products/scrape (add new product by URL)
- GET /api/v1/products/{product_id}/price-history

USER MANAGEMENT:

- GET /api/v1/users/profile
- PUT /api/v1/users/profile
- GET /api/v1/users/favorites
- POST /api/v1/users/favorites/{product_id}
- DELETE /api/v1/users/favorites/{product_id}

PRICE ALERTS:

- GET /api/v1/alerts/
- POST /api/v1/alerts/
- PUT /api/v1/alerts/{alert_id}
- DELETE /api/v1/alerts/{alert_id}

DEALS:

- GET /api/v1/deals/ (community deals with pagination)
- POST /api/v1/deals/ (submit new deal)
- PUT /api/v1/deals/{deal_id}/vote (upvote/downvote)

REQUIREMENTS:

- Implement proper pagination for list endpoints
- Add comprehensive input validation
- Include proper error responses with status codes
- Add database indexes for performance
- Implement soft deletes where appropriate
- Include API documentation with examples

OUTPUT: Complete database models with relationships, migrations, and fully functional API endpoints with proper validation and error handling.

Create a comprehensive Streamlit testing interface for the Amazon scraping FastAPI backend:

STREAMLIT APP STRUCTURE:
streamlit*app/
├── main.py (main Streamlit app)
├── pages/
│ ├── 1*🔍*Product_Scraping.py
│ ├── 2*📊*Price_Tracking.py
│ ├── 3*🔔*Alerts_Management.py
│ ├── 4*❤️*Favorites.py
│ ├── 5*🎯*Deals.py
│ └── 6*⚙️_Admin_Panel.py
├── components/
│ ├── auth.py (authentication components)
│ ├── api_client.py (FastAPI client)
│ └── charts.py (price history charts)
├── utils/
│ └── helpers.py
└── config.py

MAIN FEATURES:

1. Authentication System:

- Login/Register forms with session state management
- JWT token storage in Streamlit session
- Auto-refresh token mechanism
- Logout functionality

2. Product Scraping Testing:

- URL input field for Amazon India product links
- Real-time scraping status with progress bar
- Display extracted data: title, images, prices, reviews
- Validation of scraped data accuracy
- Export scraped data to CSV/JSON

3. Price Tracking Dashboard:

- Product search and selection interface
- Interactive price history charts using Plotly
- Price trend analysis with statistics
- Comparison between multiple products

4. Alerts Management:

- Create new price alerts with target price
- View active alerts in a data table
- Test alert notifications (send test email/SMS)
- Alert performance metrics

5. Favorites Management:

- Add/remove products from favorites
- Bulk operations on favorites
- Export favorites list

6. Deals Testing:

- Submit new deals with validation
- View community deals with voting
- Deal verification workflow

7. Admin Panel:

- System health monitoring
- Background task status
- Database statistics
- User management

TECHNICAL REQUIREMENTS:

- Responsive design with proper styling
- Error handling with user-friendly messages
- Loading states and progress indicators
- Data validation and input sanitization
- API response caching for better performance

API INTEGRATION:

- HTTP client with authentication headers
- Proper error handling for API failures
- Retry mechanisms for failed requests
- Real-time updates using Streamlit's auto-refresh

TESTING FEATURES:

- API endpoint testing with different parameters
- Data validation checks
- Performance monitoring (response times)
- Error scenario testing

VISUALIZATION:

- Price history line charts
- Product comparison charts
- Alert statistics pie charts
- System performance dashboards

OUTPUT: Complete Streamlit application for comprehensive testing of all FastAPI backend features with intuitive UI and robust error handling.

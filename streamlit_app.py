import streamlit as st
import requests
import json

FASTAPI_URL = "http://localhost:8000"

st.set_page_config(page_title="HatkeDeal Scraper", layout="wide")

# --- Helper Functions ---
def register_user(username, email, password):
    response = requests.post(
        f"{FASTAPI_URL}/api/v1/register",
        json={"username": username, "email": email, "password": password}
    )
    return response.json(), response.status_code

def login_user(username, password):
    response = requests.post(
        f"{FASTAPI_URL}/api/v1/login/access-token",
        data={"username": username, "password": password}
    )
    return response.json(), response.status_code

def scrape_product(url, token):
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(
        f"{FASTAPI_URL}/api/v1/scrape",
        json={"url": url},
        headers=headers
    )
    return response.json(), response.status_code

def save_product(product_data, token):
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(
        f"{FASTAPI_URL}/api/v1/products",
        json=product_data,
        headers=headers
    )
    return response.json(), response.status_code

def get_saved_products(token):
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(
        f"{FASTAPI_URL}/api/v1/products",
        headers=headers
    )
    return response.json(), response.status_code

# --- Streamlit UI ---
st.title("HatkeDeal Scraper")

if "logged_in" not in st.session_state:
    st.session_state.logged_in = False
if "token" not in st.session_state:
    st.session_state.token = None
if "user_role" not in st.session_state:
    st.session_state.user_role = None
if "username" not in st.session_state:
    st.session_state.username = None

# Sidebar for Login/Register
with st.sidebar:
    st.header("Authentication")
    if not st.session_state.logged_in:
        auth_option = st.radio("Choose an option", ["Login", "Register"])

        if auth_option == "Login":
            st.subheader("Login")
            username = st.text_input("Username", key="login_username")
            password = st.text_input("Password", type="password", key="login_password")
            if st.button("Login"):
                data, status = login_user(username, password)
                if status == 200:
                    st.session_state.logged_in = True
                    st.session_state.token = data["access_token"]
                    st.session_state.username = username
                    # Fetch user role after successful login
                    user_profile, profile_status = requests.get(
                        f"{FASTAPI_URL}/api/v1/user/me",
                        headers={"Authorization": f"Bearer {st.session_state.token}"}
                    ).json(), profile_status = requests.get(
                        f"{FASTAPI_URL}/api/v1/user/me",
                        headers={"Authorization": f"Bearer {st.session_state.token}"}
                    ).status_code
                    if profile_status == 200:
                        st.session_state.user_role = user_profile.get("role")
                    st.success(f"Logged in as {username} ({st.session_state.user_role})")
                    st.experimental_rerun()
                else:
                    st.error(data.get("detail", "Login failed"))
        else: # Register
            st.subheader("Register")
            new_username = st.text_input("New Username")
            new_email = st.text_input("Email")
            new_password = st.text_input("New Password", type="password")
            if st.button("Register"):
                data, status = register_user(new_username, new_email, new_password)
                if status == 201:
                    st.success("Registration successful! Please login.")
                else:
                    st.error(data.get("detail", "Registration failed"))
    else:
        st.write(f"Welcome, {st.session_state.username} ({st.session_state.user_role})!")
        if st.button("Logout"):
            st.session_state.logged_in = False
            st.session_state.token = None
            st.session_state.user_role = None
            st.session_state.username = None
            st.experimental_rerun()

# Main Content
if st.session_state.logged_in:
    st.header("Product Scraper")
    product_url = st.text_input("Enter Product URL to Scrape (e.g., Amazon product page)")

    if st.button("Scrape Product"):
        if product_url:
            with st.spinner("Scraping product details..."):
                scraped_data, status = scrape_product(product_url, st.session_state.token)
                if status == 200:
                    st.session_state.scraped_product = scraped_data
                    st.success("Product scraped successfully!")
                    st.subheader("Scraped Product Details:")
                    st.json(scraped_data)
                else:
                    st.error(scraped_data.get("detail", "Failed to scrape product."))
        else:
            st.warning("Please enter a product URL.")

    if "scraped_product" in st.session_state and st.session_state.scraped_product:
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Save Product", key="save_product_btn"):
                with st.spinner("Saving product..."):
                    save_response, save_status = save_product(st.session_state.scraped_product, st.session_state.token)
                    if save_status == 201:
                        st.success("Product saved successfully!")
                        st.session_state.scraped_product = None # Clear scraped data after saving
                    else:
                        st.error(save_response.get("detail", "Failed to save product."))
        with col2:
            if st.button("Discard Product", key="discard_product_btn"):
                st.session_state.scraped_product = None
                st.info("Scraped product discarded.")

    st.markdown("---")
    st.header("Saved Products")
    if st.button("Refresh Saved Products"):
        with st.spinner("Fetching saved products..."):
            saved_products_data, saved_products_status = get_saved_products(st.session_state.token)
            if saved_products_status == 200:
                if saved_products_data and saved_products_data.get("data"):
                    st.subheader("Your Saved Products:")
                    for product in saved_products_data["data"]:
                        st.json(product)
                else:
                    st.info("No products saved yet.")
            else:
                st.error(saved_products_data.get("detail", "Failed to fetch saved products."))
else:
    st.info("Please login or register to use the application.")

from datetime import datetime
from decimal import Decimal
from typing import Annotated
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from ..models.product_alert import AlertType


class ProductAlertBase(BaseModel):
    product_id: int
    target_price: Annotated[Decimal, Field(ge=0, examples=[50000.00])]
    alert_type: AlertType = AlertType.EMAIL
    is_active: bool = True


class ProductAlert(ProductAlertBase):
    model_config = ConfigDict(from_attributes=True)


class ProductAlertRead(ProductAlertBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    uuid: UUID
    user_id: int
    created_at: datetime
    updated_at: datetime | None
    is_deleted: bool


class ProductAlertCreate(ProductAlertBase):
    pass


class ProductAlertCreateInternal(ProductAlertCreate):
    user_id: int


class ProductAlertUpdate(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    target_price: Annotated[Decimal | None, Field(default=None, ge=0)]
    alert_type: AlertType | None = None
    is_active: bool | None = None


class ProductAlertUpdateInternal(ProductAlertUpdate):
    updated_at: datetime


class ProductAlertDelete(BaseModel):
    model_config = ConfigDict(extra="forbid")
    
    is_deleted: bool
    deleted_at: datetime

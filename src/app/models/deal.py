import uuid as uuid_pkg
from datetime import UTC, datetime

from sqlalchemy import DateTime, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.db.database import Base


class Deal(Base):
    __tablename__ = "deal"

    id: Mapped[int] = mapped_column("id", autoincrement=True, nullable=False, unique=True, primary_key=True, init=False)
    
    # Foreign keys
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    product_id: Mapped[int] = mapped_column(ForeignKey("product.id"), index=True)
    
    # Deal information
    deal_title: Mapped[str] = mapped_column(String(200))
    deal_description: Mapped[str | None] = mapped_column(Text, default=None)
    deal_url: Mapped[str] = mapped_column(String(500))
    
    # Deal status and metrics
    is_verified: Mapped[bool] = mapped_column(default=False, index=True)
    upvotes: Mapped[int] = mapped_column(default=0)
    
    # Standard fields
    uuid: Mapped[uuid_pkg.UUID] = mapped_column(default_factory=uuid_pkg.uuid4, unique=True)
    submitted_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default_factory=lambda: datetime.now(UTC))
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default_factory=lambda: datetime.now(UTC))
    updated_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    deleted_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None)
    is_deleted: Mapped[bool] = mapped_column(default=False, index=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="deals", init=False)
    product: Mapped["Product"] = relationship("Product", back_populates="deals", init=False)

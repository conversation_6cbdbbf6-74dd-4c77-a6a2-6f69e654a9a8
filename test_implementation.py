#!/usr/bin/env python3
"""
Test script to validate the Amazon scraper implementation.
This script tests the basic functionality without requiring <PERSON><PERSON>.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from app.scraping.base_scraper import ProductData, ScrapingConfig
from app.scraping.ai_agent import AIParsingAgent
from app.scraping.utils.user_agents import user_agent_manager
from app.scraping.utils.anti_detection import anti_detection_manager
from app.core.config import settings


async def test_user_agent_manager():
    """Test user agent management."""
    print("🧪 Testing User Agent Manager...")
    
    # Test getting random user agent
    ua1 = user_agent_manager.get_random_user_agent()
    ua2 = user_agent_manager.get_random_user_agent()
    
    print(f"✅ User Agent 1: {ua1[:50]}...")
    print(f"✅ User Agent 2: {ua2[:50]}...")
    
    # Test headers
    headers = user_agent_manager.get_headers()
    print(f"✅ Headers generated: {len(headers)} headers")
    
    # Test mobile headers
    mobile_headers = user_agent_manager.get_mobile_headers()
    print(f"✅ Mobile headers generated: {len(mobile_headers)} headers")
    
    print("✅ User Agent Manager tests passed!\n")


async def test_anti_detection_manager():
    """Test anti-detection functionality."""
    print("🧪 Testing Anti-Detection Manager...")
    
    # Test delay calculation
    delay = anti_detection_manager.calculate_adaptive_delay()
    print(f"✅ Adaptive delay calculated: {delay:.2f} seconds")
    
    # Test CAPTCHA detection
    captcha_html = "<html><body>Please solve this captcha to continue</body></html>"
    is_captcha = anti_detection_manager.detect_captcha(captcha_html)
    print(f"✅ CAPTCHA detection: {is_captcha}")
    
    # Test rate limiting detection
    rate_limit_html = "<html><body>Too many requests, please try again later</body></html>"
    is_rate_limited = anti_detection_manager.detect_rate_limiting(rate_limit_html, 429)
    print(f"✅ Rate limit detection: {is_rate_limited}")
    
    # Test blocking detection
    blocked_html = "<html><body>Access denied</body></html>"
    is_blocked = anti_detection_manager.detect_blocking(blocked_html, 403)
    print(f"✅ Blocking detection: {is_blocked}")
    
    # Test stats
    stats = anti_detection_manager.get_stats()
    print(f"✅ Anti-detection stats: {len(stats)} metrics")
    
    print("✅ Anti-Detection Manager tests passed!\n")


async def test_ai_parsing_agent():
    """Test AI parsing agent (without actual API calls)."""
    print("🧪 Testing AI Parsing Agent...")
    
    ai_agent = AIParsingAgent()
    
    # Test basic HTML parsing methods
    sample_html = """
    <html>
        <head><title>Test Product</title></head>
        <body>
            <h1 id="productTitle">Apple iPhone 13 (128GB) - Blue</h1>
            <span class="a-price-whole">69,900</span>
            <span class="a-text-price">₹79,900</span>
            <div id="bylineInfo">Brand: Apple</div>
            <div class="a-icon-alt">4.5 out of 5 stars</div>
            <span id="acrCustomerReviewText">1,234 ratings</span>
        </body>
    </html>
    """
    
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(sample_html, 'html.parser')
    
    # Test basic extraction methods
    title = ai_agent._extract_title(soup)
    print(f"✅ Title extraction: {title}")
    
    current_price, original_price = ai_agent._extract_prices(soup)
    print(f"✅ Price extraction: Current={current_price}, Original={original_price}")
    
    brand = ai_agent._extract_brand(soup)
    print(f"✅ Brand extraction: {brand}")
    
    rating, reviews = ai_agent._extract_rating_and_reviews(soup)
    print(f"✅ Rating extraction: {rating} stars, {reviews} reviews")
    
    # Test ASIN extraction
    test_url = "https://www.amazon.in/dp/B08N5WRWNW/ref=sr_1_1"
    asin = ai_agent._extract_asin_from_url(test_url)
    print(f"✅ ASIN extraction: {asin}")
    
    print("✅ AI Parsing Agent tests passed!\n")


async def test_product_data():
    """Test ProductData structure."""
    print("🧪 Testing ProductData Structure...")
    
    # Create sample product data
    product = ProductData(
        amazon_asin="B08N5WRWNW",
        title="Apple iPhone 13 (128GB) - Blue",
        description="Latest iPhone with A15 Bionic chip",
        brand="Apple",
        category="Electronics",
        images=["https://example.com/image1.jpg"],
        current_price=69900.00,
        original_price=79900.00,
        discount_percentage=12,
        availability_status="in_stock",
        average_rating=4.5,
        total_reviews=1234
    )
    
    print(f"✅ Product created: {product.title}")
    print(f"✅ ASIN: {product.amazon_asin}")
    print(f"✅ Price: ₹{product.current_price} (was ₹{product.original_price})")
    print(f"✅ Discount: {product.discount_percentage}%")
    print(f"✅ Rating: {product.average_rating}/5 ({product.total_reviews} reviews)")
    print(f"✅ Availability: {product.availability_status}")
    
    print("✅ ProductData tests passed!\n")


async def test_scraping_config():
    """Test scraping configuration."""
    print("🧪 Testing Scraping Configuration...")
    
    config = ScrapingConfig()
    
    print(f"✅ Delay range: {config.delay_min}-{config.delay_max} seconds")
    print(f"✅ Timeout: {config.timeout} seconds")
    print(f"✅ Retries: {config.retries}")
    print(f"✅ User agent rotation: {config.user_agent_rotation}")
    print(f"✅ Proxy enabled: {config.proxy_enabled}")
    print(f"✅ Respect robots.txt: {config.respect_robots}")
    
    print("✅ Scraping Configuration tests passed!\n")


async def test_settings():
    """Test application settings."""
    print("🧪 Testing Application Settings...")
    
    print(f"✅ App name: {settings.APP_NAME}")
    print(f"✅ Environment: {settings.ENVIRONMENT}")
    print(f"✅ Database: {settings.POSTGRES_DB}")
    print(f"✅ Amazon base URL: {settings.AMAZON_BASE_URL}")
    print(f"✅ Scraping timeout: {settings.SCRAPING_TIMEOUT}")
    print(f"✅ OpenAI API key configured: {'Yes' if settings.OPENAI_API_KEY and settings.OPENAI_API_KEY != 'your_openai_api_key_here' else 'No'}")
    
    print("✅ Application Settings tests passed!\n")


async def main():
    """Run all tests."""
    print("🚀 Starting Amazon Scraper Implementation Tests...\n")
    
    try:
        await test_settings()
        await test_scraping_config()
        await test_product_data()
        await test_user_agent_manager()
        await test_anti_detection_manager()
        await test_ai_parsing_agent()
        
        print("🎉 All tests passed successfully!")
        print("\n📋 Implementation Summary:")
        print("✅ Database models created (Product, PriceHistory, ProductAlert, UserFavorite, Deal)")
        print("✅ Pydantic schemas implemented")
        print("✅ CRUD operations configured")
        print("✅ API endpoints implemented:")
        print("   - Authentication (login, register, logout)")
        print("   - Products (CRUD, search, price history)")
        print("   - User favorites management")
        print("   - Price alerts management")
        print("   - Community deals with voting")
        print("   - Scraping endpoints (product, search, status)")
        print("✅ Scraping system implemented:")
        print("   - Base scraper with strategy pattern")
        print("   - AI-powered parsing agent")
        print("   - BeautifulSoup, Selenium, and Playwright strategies")
        print("   - Anti-detection utilities")
        print("   - Proxy and user agent management")
        print("✅ Configuration updated with scraping settings")
        print("✅ Dependencies added to pyproject.toml")
        print("✅ Database migration created")
        
        print("\n🔧 Next Steps:")
        print("1. Install new dependencies: pip install -e .")
        print("2. Start Docker services: docker compose up -d")
        print("3. Run migrations: alembic upgrade head")
        print("4. Test API endpoints using the FastAPI docs at http://localhost:8000/docs")
        print("5. Configure OpenAI/Anthropic API keys for AI-powered scraping")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

# Pull Request Template for FastAPI-boilerplate

## Description
Please provide a clear and concise description of what your pull request is about.

## Changes
Briefly list the changes you've made. If applicable, also link any relevant issues or pull requests.

## Tests
Describe the tests you added or modified to cover your changes, if applicable.

## Checklist
- [ ] I have read the [CONTRIBUTING](CONTRIBUTING.md) document.
- [ ] My code follows the code style of this project.
- [ ] I have added necessary documentation (if appropriate).
- [ ] I have added tests that cover my changes (if applicable).
- [ ] All new and existing tests passed.

## Additional Notes
Include any additional information that you think is important for reviewers to know.

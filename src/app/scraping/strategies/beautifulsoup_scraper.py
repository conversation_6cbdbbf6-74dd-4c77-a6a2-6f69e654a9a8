"""BeautifulSoup-based scraper for lightweight HTML parsing."""

import asyncio
from typing import List, Optional
from urllib.parse import urljoin, quote

import aiohttp
from bs4 import BeautifulSoup

from ..base_scraper import BaseScraper, ProductData, ScrapingConfig
from ..ai_agent import ai_parsing_agent
from ..exceptions import (
    InvalidURLException, 
    ProductNotFoundException, 
    NetworkTimeoutException,
    CaptchaDetectedException,
    RateLimitedException
)
from ..utils.anti_detection import anti_detection_manager
from ..utils.proxy_manager import proxy_manager


class BeautifulSoupScraper(BaseScraper):
    """BeautifulSoup-based scraper for lightweight HTML parsing."""
    
    def __init__(self, config: Optional[ScrapingConfig] = None):
        super().__init__(config)
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self) -> None:
        """Initialize the HTTP session."""
        connector = aiohttp.TCPConnector(
            limit=10,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=anti_detection_manager.get_adaptive_headers()
        )
    
    async def cleanup(self) -> None:
        """Cleanup the HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def scrape_product(self, url: str) -> ProductData:
        """
        Scrape a single product from the given URL.
        
        Args:
            url: Amazon product URL
            
        Returns:
            ProductData: Scraped product information
        """
        if not self.validate_url(url):
            raise InvalidURLException(f"Invalid Amazon India URL: {url}")
        
        # Apply delay before request
        await self.apply_delay()
        
        # Get HTML content
        html_content = await self._fetch_html(url)
        
        # Parse with AI agent
        try:
            product_data = await ai_parsing_agent.parse_product_page(html_content, url)
            
            # Validate required fields
            if not product_data.amazon_asin:
                product_data.amazon_asin = self.extract_asin(url) or ""
            
            if not product_data.title:
                raise ProductNotFoundException("Could not extract product title")
            
            return product_data
            
        except Exception as e:
            raise ProductNotFoundException(f"Failed to parse product data: {str(e)}")
    
    async def scrape_search_results(self, query: str, max_results: int = 10) -> List[ProductData]:
        """
        Scrape products from search results.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List[ProductData]: List of scraped products
        """
        search_url = self._build_search_url(query)
        
        # Apply delay before request
        await self.apply_delay()
        
        # Get search results HTML
        html_content = await self._fetch_html(search_url)
        
        # Parse search results
        product_urls = self._extract_product_urls_from_search(html_content)
        
        # Limit results
        product_urls = product_urls[:max_results]
        
        # Scrape individual products
        products = []
        for product_url in product_urls:
            try:
                # Apply delay between product scrapes
                await self.apply_delay()
                
                product_data = await self.scrape_product(product_url)
                products.append(product_data)
                
            except Exception as e:
                # Continue with other products if one fails
                print(f"Failed to scrape product {product_url}: {str(e)}")
                continue
        
        return products
    
    async def _fetch_html(self, url: str) -> str:
        """
        Fetch HTML content from URL.
        
        Args:
            url: URL to fetch
            
        Returns:
            str: HTML content
        """
        if not self.session:
            raise RuntimeError("Session not initialized")
        
        # Update request stats
        anti_detection_manager.update_request_stats()
        
        # Get proxy configuration
        proxy = proxy_manager.get_proxy_config()
        
        # Prepare request parameters
        kwargs = {
            'headers': anti_detection_manager.get_adaptive_headers(),
        }
        
        if proxy:
            kwargs['proxy'] = proxy
        
        try:
            async with self.session.get(url, **kwargs) as response:
                # Check for rate limiting or blocking
                if response.status == 429:
                    raise RateLimitedException("Rate limited by server")
                
                if response.status in [403, 406]:
                    await anti_detection_manager.handle_detection('blocking')
                    raise RateLimitedException("Request blocked by server")
                
                if response.status != 200:
                    raise NetworkTimeoutException(f"HTTP {response.status}: {response.reason}")
                
                html_content = await response.text()
                
                # Check for CAPTCHA or other detection
                if anti_detection_manager.detect_captcha(html_content):
                    await anti_detection_manager.handle_detection('captcha')
                    raise CaptchaDetectedException("CAPTCHA detected")
                
                if anti_detection_manager.detect_rate_limiting(html_content, response.status):
                    await anti_detection_manager.handle_detection('rate_limit')
                    raise RateLimitedException("Rate limiting detected")
                
                if anti_detection_manager.detect_blocking(html_content, response.status):
                    await anti_detection_manager.handle_detection('blocking')
                    raise RateLimitedException("Request blocking detected")
                
                return html_content
                
        except aiohttp.ClientError as e:
            raise NetworkTimeoutException(f"Network error: {str(e)}")
        except asyncio.TimeoutError:
            raise NetworkTimeoutException("Request timeout")
    
    def _build_search_url(self, query: str) -> str:
        """
        Build Amazon search URL.
        
        Args:
            query: Search query
            
        Returns:
            str: Search URL
        """
        base_url = "https://www.amazon.in/s"
        encoded_query = quote(query)
        return f"{base_url}?k={encoded_query}&ref=sr_pg_1"
    
    def _extract_product_urls_from_search(self, html_content: str) -> List[str]:
        """
        Extract product URLs from search results.
        
        Args:
            html_content: Search results HTML
            
        Returns:
            List[str]: List of product URLs
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        product_urls = []
        
        # Find product links in search results
        selectors = [
            'h2.a-size-mini a',
            '.s-link-style a',
            '[data-component-type="s-search-result"] h2 a',
            '.s-result-item h2 a'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        full_url = urljoin('https://www.amazon.in', href)
                    else:
                        full_url = href
                    
                    # Validate and add URL
                    if self.validate_url(full_url) and full_url not in product_urls:
                        product_urls.append(full_url)
            
            # Break if we found products with this selector
            if product_urls:
                break
        
        return product_urls
    
    async def _retry_request(self, url: str, max_retries: int = None) -> str:
        """
        Retry request with exponential backoff.
        
        Args:
            url: URL to fetch
            max_retries: Maximum number of retries
            
        Returns:
            str: HTML content
        """
        max_retries = max_retries or self.config.retries
        
        for attempt in range(max_retries + 1):
            try:
                return await self._fetch_html(url)
            except (RateLimitedException, CaptchaDetectedException) as e:
                if attempt == max_retries:
                    raise e
                
                # Exponential backoff with jitter
                delay = (2 ** attempt) + (asyncio.get_event_loop().time() % 1)
                await asyncio.sleep(delay)
                
                # Rotate proxy and user agent
                anti_detection_manager.rotate_proxy()
                anti_detection_manager.rotate_user_agent()
                
            except Exception as e:
                if attempt == max_retries:
                    raise e
                
                # Short delay for other errors
                await asyncio.sleep(1)
        
        raise NetworkTimeoutException("Max retries exceeded")
